package net.summerfarm.wnc.domain.fence.repository;



import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;

import java.util.List;


/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncFenceChangeRecordsCommandRepository {

    WncFenceChangeRecordsEntity insertSelective(WncFenceChangeRecordsCommandParam param);

    int updateSelectiveById(WncFenceChangeRecordsCommandParam param);

    int remove(Long id);

    /**
     * 取消切仓任务
     * @param fenceChangeTaskId 切仓任务ID
     */
    void cancelFenceChangeTask(Long fenceChangeTaskId);

    /**
     * 批量插入
     * @param params 参数列表
     * @return 插入的记录数
     */
    int batchInsert(List<WncFenceChangeRecordsCommandParam> params);

    /**
     * 根据ID集合删除围栏变更记录
     * @param wncFenceChangeRecordsIds 围栏变更记录ID集合
     */
    void deleteByIds(List<Long> wncFenceChangeRecordsIds);

    /**
     * 根据变更批次号更新切仓任务ID
     * @param fenceChangeTaskId 切仓任务ID
     * @param changeBatchNo 变更批次号
     */
    void updateFenceChangeTaskIdByChangeBatchNo(Long fenceChangeTaskId, String changeBatchNo);
}
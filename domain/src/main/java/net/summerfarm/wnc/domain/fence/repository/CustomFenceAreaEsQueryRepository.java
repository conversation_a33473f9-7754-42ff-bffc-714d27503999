package net.summerfarm.wnc.domain.fence.repository;

import java.util.List;

/**
 * Description: <br/>
 * date: 2025/9/3 11:07<br/>
 *
 * <AUTHOR> />
 */
public interface CustomFenceAreaEsQueryRepository {
    /**
     * 根据adCodeMsgId和订单POI查询匹配的区域
     */
    Integer matchEsByAdCodeMsgIdsWithPoi(List<Integer> adCodeMsgIds, String orderPoi);

    /**
     * 根据adCodeMsgId查询POI
     */
    String findPoiByAdCodeMsgIds(List<Integer> adCodeMsgIds);
}

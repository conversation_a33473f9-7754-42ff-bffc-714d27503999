package net.summerfarm.wnc.domain.fence.param.query;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Data
public class WncCityAreaChangeWarehouseRecordsQueryParam extends BasePageInput {
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 区域
	 */
	private String area;

	/**
	 * 区域集合
	 */
	private List<String> areaList;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 城市集合
	 */
	private List<String> cityList;

	/**
	 * 变更状态 0 等待生效 10正常生效中 20已结束
	 */
	private Integer changeStatus;

	/**
	 * 变更状态 0 等待生效 10正常生效中 20已结束
	 */
	private List<Integer> changeStatusList;

	/**
	 * 预约执行切仓时间
	 */
	private LocalDateTime preExeTime;

	/**
	 * 围栏变更批次号
	 */
	private String changeBatchNo;

	/**
	 * 切仓任务ID
	 */
	private Long fenceChangeTaskId;

	/**
	 * 生效时间
	 */
	private LocalDateTime effectiveTime;

	/**
	 * 完结结束时间
	 */
	private LocalDateTime overTime;

	/**
	 * 区域定义类型10 普通范围区域  20自定义范围区域
	 */
	private Integer areaDefinationType;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 切仓任务ID
	 */
	private List<Long> fenceChangeTaskIds;

	/**
	 * 围栏名称
	 */
	private String fenceName;
	

	
}
package net.summerfarm.wnc.domain.fence.service;

import net.summerfarm.wnc.common.enums.WncFenceAreaChangeRecordsEnums;
import net.summerfarm.wnc.domain.fence.HistoryOutOfCustomFenceAreaESRepository;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义围栏区域Es操作服务
 * date: 2025/9/3 11:14<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CustomFenceAreaEsCommandDomainService {

    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;

    /**
     * 创建自定义围栏区域
     * @param fenceChangeRecordIds 区域变更记录id集合
     */
    public void createCustomFenceArea(List<Long> fenceChangeRecordIds) {
        if (CollectionUtils.isEmpty(fenceChangeRecordIds)) {
            return;
        }
        // 查询已绘制区域
        WncFenceAreaChangeRecordsQueryParam param = new WncFenceAreaChangeRecordsQueryParam();
        param.setFenceChangeIds(fenceChangeRecordIds);
        List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntities = wncFenceAreaChangeRecordsQueryRepository.selectByCondition(param);
        if (CollectionUtils.isEmpty(wncFenceAreaChangeRecordsEntities)) {
            return;
        }

        List<CustomFenceAreaEsEntity> customFenceAreaEsEntities = wncFenceAreaChangeRecordsEntities.stream().map(e -> {
            CustomFenceAreaEsEntity customFenceAreaEsEntity = new CustomFenceAreaEsEntity();

            customFenceAreaEsEntity.setId(String.valueOf(e.getId()));
            customFenceAreaEsEntity.setAdCodeMsgId(e.getAdCodeMsgId());
            customFenceAreaEsEntity.setProvince(e.getAdCodeMsgDetailEntity().getProvince());
            customFenceAreaEsEntity.setCity(e.getCity());
            customFenceAreaEsEntity.setArea(e.getArea());
            customFenceAreaEsEntity.setGeoShape(e.getGeoShape());
            customFenceAreaEsEntity.setStatus(e.getAdCodeMsgDetailEntity().getStatus());

            return customFenceAreaEsEntity;
        }).collect(Collectors.toList());

        customFenceAreaEsCommandRepository.saveAll(customFenceAreaEsEntities);
    }
}

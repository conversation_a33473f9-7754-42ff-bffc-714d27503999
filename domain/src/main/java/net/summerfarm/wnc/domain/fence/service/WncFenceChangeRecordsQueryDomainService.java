package net.summerfarm.wnc.domain.fence.service;


import net.summerfarm.wnc.common.enums.FenceChangeTypeEnum;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * @Title: 围栏变更执行记录表领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Service
public class WncFenceChangeRecordsQueryDomainService {

    /**
     * 确定围栏变更类型
     */
    public FenceChangeTypeEnum determineCustomFenceChangeType(List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords,
                                                              List<WncFenceChangeRecordsEntity> afterFenceChangeRecords) {
        if (CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            return FenceChangeTypeEnum.NONE_TO_CUSTOM;
        }

        List<FenceEntity> beforeFenceEntityList = beforeFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getFenceDetailEntity)
                .collect(Collectors.toList());

        List<FenceEntity> afterFenceEntityList = afterFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getFenceDetailEntity)
                .collect(Collectors.toList());

        List<FenceEnums.Type> beforeFenceType = beforeFenceEntityList.stream()
                .map(FenceEntity::getType)
                .distinct()
                .collect(Collectors.toList());

        List<FenceEnums.Type> afterFenceType = afterFenceEntityList.stream()
                .map(FenceEntity::getType)
                .distinct()
                .collect(Collectors.toList());

        if (beforeFenceType.size() == 1 && beforeFenceType.contains(FenceEnums.Type.CUSTOM)) {
            return FenceChangeTypeEnum.CUSTOM_TO_CUSTOM;
        } else if (beforeFenceType.size() == 1 && !beforeFenceType.contains(FenceEnums.Type.CUSTOM)
                && afterFenceType.contains(FenceEnums.Type.CUSTOM)) {
            return FenceChangeTypeEnum.NORMAL_TO_CUSTOM;
        } else {
            return FenceChangeTypeEnum.UNKNOWN;
        }
    }

}

package net.summerfarm.wnc.domain.fence.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description:地址参数
 * date: 2024/1/4 14:11
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressParam implements Serializable {

    private static final long serialVersionUID = 4147985519812087740L;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 详细地址
     */
    private String address;
}

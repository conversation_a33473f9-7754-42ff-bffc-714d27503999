package net.summerfarm.wnc.domain.fence.service;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.AdCodeMsgEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.common.query.fence.AdCodeMsgQuery;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.AreaRepository;
import net.summerfarm.wnc.domain.fence.FenceDomainService;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.param.command.*;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.*;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsMappingRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseStorageCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseStorageEntity;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 *
 * @Title: 围栏区域变更记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncFenceAreaChangeRecordsCommandDomainService {


    @Autowired
    private WncFenceAreaChangeRecordsCommandRepository wncFenceAreaChangeRecordsCommandRepository;
    @Autowired
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Autowired
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;
    @Autowired
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Autowired
    private AdCodeMsgRepository adCodeMsgRepository;
    @Autowired
    private FenceDomainService fenceDomainService;
    @Autowired
    private WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    @Autowired
    private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;
    @Autowired
    private WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    @Autowired
    private AreaRepository areaRepository;



    public WncFenceAreaChangeRecordsEntity insert(WncFenceAreaChangeRecordsCommandParam param) {
        return wncFenceAreaChangeRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncFenceAreaChangeRecordsCommandParam param) {
        return wncFenceAreaChangeRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncFenceAreaChangeRecordsCommandRepository.remove(id);
    }

    /**
     * 更新围栏区域变更记录的围栏ID和ACM区域ID
     * @param params 围栏区域变更记录
     */
    public void updateAdCodeIdAndFenceId(List<WncFenceAreaChangeRecordsCommandParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        wncFenceAreaChangeRecordsCommandRepository.batchUpdate(params);
    }

    /**
     * 新增自定义围栏变更记录
     * @param param 新增参数
     * @return 围栏变更记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addCustomFenceChangeRecord(AddCustomFenceChangeRecordCommandParam param) {
        // 根据cityAreaChangeWarehouseRecordId查询省市区
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(param.getCityAreaChangeWarehouseRecordId());
        if (cityAreaRecord == null) {
            throw new BizException("城市区域变更记录不存在");
        }

        // 新增变更前围栏数据
        addBeforeChangeRecord(cityAreaRecord.getProvince(), cityAreaRecord.getCity(), cityAreaRecord.getArea(), cityAreaRecord.getChangeBatchNo());

        // 新增变更后围栏数据
        Long fenceChangeRecordsId = addAfterChangeRecord(cityAreaRecord.getChangeBatchNo(), param);

        return fenceChangeRecordsId;
    }

    /**
     * 新增变更前的围栏记录
     */
    private void addBeforeChangeRecord(String province, String city, String area, String changeBatchNo) {
        // 根据省市区查询ad_code_msg
        AdCodeMsgQuery adCodeMsgQuery = AdCodeMsgQuery.builder()
                .province(province)
                .city(city)
                .area(area)
                .status(AdCodeMsgEnums.Status.VALID.getValue())
                .build();
        List<AdCodeMsgEntity> adCodeMsgEntities = adCodeMsgRepository.queryList(adCodeMsgQuery);

        if (CollectionUtils.isEmpty(adCodeMsgEntities)) {
            log.info("未找到对应的区域信息，province: {}, city: {}, area: {}", province, city, area);
            return;
        }

        // 获取所有涉及的围栏ID
        List<Integer> fenceIds = adCodeMsgEntities.stream()
                .map(AdCodeMsgEntity::getFenceId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<FenceEntity> fenceEntities = fenceDomainService.queryFenceAllDetailList(fenceIds);
        Map<Integer, FenceEntity> fenceEntityMap = fenceEntities.stream().collect(Collectors.toMap(FenceEntity::getId, Function.identity()));
        List<Integer> storeNoList = fenceEntities.stream().map(FenceEntity::getStoreNo).collect(Collectors.toList());
        // 查询映射
        Map<Integer, List<Integer>> storeWarehouseMap = warehouseLogisticsMappingRepository.queryLogisticsMapping(storeNoList);
        // 城配仓名称
        Map<Integer, String> storeNoToNameMap = warehouseLogisticsCenterRepository.queryStoreNoToNameMapByStoreNos(storeNoList);
        // 运营区域名称
        List<Integer> areaNoList = fenceEntities.stream().map(FenceEntity::getAreaNo).collect(Collectors.toList());
        Map<Integer, String> areaNoToNameMap = areaRepository.queryAreaNoToNameMapByAreaNos(areaNoList);

        WncFenceChangeRecordsQueryParam param = new WncFenceChangeRecordsQueryParam();
        param.setChangeBatchNo(changeBatchNo);
        List<WncFenceChangeRecordsEntity> changeBatchFenceRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(param);
        Map<Integer, List<WncFenceChangeRecordsEntity>> fenceIdToFenceChangeRecordsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(changeBatchFenceRecords)) {
            fenceIdToFenceChangeRecordsMap = changeBatchFenceRecords.stream()
                    .filter(e -> e.getFenceId() != null)
                    .collect(Collectors.groupingBy(WncFenceChangeRecordsEntity::getFenceId));
        }

        // 为每个围栏创建变更前记录
        for (Integer fenceId : fenceIds) {
            if (fenceIdToFenceChangeRecordsMap.get(fenceId) != null) {
                // 已存在
                continue;
            }
            // 围栏变更记录组装
            WncFenceChangeRecordsCommandParam fenceChangeParam = new WncFenceChangeRecordsCommandParam();
            fenceChangeParam.setFenceId(fenceId);
            fenceChangeParam.setChangeBatchNo(changeBatchNo);
            fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
            // 现有围栏信息
            FenceEntity fenceEntity = fenceEntityMap.get(fenceId);
            if (null != fenceEntity) {
                fenceChangeParam.setFenceName(fenceEntity.getFenceName());
                fenceChangeParam.setFenceStoreNo(fenceEntity.getStoreNo());
                fenceChangeParam.setFenceStoreName(storeNoToNameMap.get(fenceEntity.getStoreNo()));
                fenceChangeParam.setFenceAreaNo(fenceEntity.getAreaNo());
                fenceChangeParam.setFenceAreaName(areaNoToNameMap.get(fenceEntity.getAreaNo()));
                fenceChangeParam.setFenceMsg(JSON.toJSONString(fenceEntity));
                List<Integer> warehouseNoList = storeWarehouseMap.get(fenceEntity.getStoreNo());
                if (CollectionUtils.isEmpty(warehouseNoList)) {
                    throw new BizException("未找到城配仓对应的仓库映射");
                }
                fenceChangeParam.setFenceWarehouseNos(Joiner.on(",").join(warehouseNoList));
                List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseNoList);
                if (CollectionUtils.isEmpty(warehouseStorageEntityList)) {
                    throw new BizException("未找到城配仓对应的仓库信息");
                }
                fenceChangeParam.setFenceWarehouseNames(Joiner.on(",").join(warehouseStorageEntityList.stream().map(WarehouseStorageEntity::getWarehouseName).collect(Collectors.toList())));
            }
            // 新增围栏变更记录
            WncFenceChangeRecordsEntity fenceChangeRecord = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeParam);
            if (null == fenceChangeRecord || null == fenceChangeRecord.getId()) {
                throw new BizException("新增变更前围栏变更记录失败");
            }

            // 为该围栏下的所有区域创建变更前记录
            List<AdCodeMsgEntity> fenceAdCodeMsgEntities = adCodeMsgEntities.stream()
                    .filter(entity -> Objects.equals(entity.getFenceId(), fenceId))
                    .collect(Collectors.toList());
            for (AdCodeMsgEntity adCodeMsgEntity : fenceAdCodeMsgEntities) {
                WncFenceAreaChangeRecordsCommandParam areaChangeParam = new WncFenceAreaChangeRecordsCommandParam();
                areaChangeParam.setFenceChangeId(fenceChangeRecord.getId());
                areaChangeParam.setFenceId(fenceId);
                areaChangeParam.setCity(adCodeMsgEntity.getCity());
                areaChangeParam.setArea(adCodeMsgEntity.getArea());
                areaChangeParam.setAdCodeMsgId(adCodeMsgEntity.getId());
                areaChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
                areaChangeParam.setAreaDrawType(adCodeMsgEntity.getAreaDrawType());
                areaChangeParam.setAdCodeMsgDetail(JSON.toJSONString(adCodeMsgEntity));
                // 新增围栏区域变更记录
                wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaChangeParam);
            }
        }
    }

    /**
     * 新增变更后的围栏记录
     */
    private Long addAfterChangeRecord(String changeBatchNo, AddCustomFenceChangeRecordCommandParam param) {
        // 查询映射
        List<Integer> warehouseNoList = warehouseLogisticsMappingRepository.queryWarehouseNosByStoreNo(param.getStoreNo());
        if (CollectionUtils.isEmpty(warehouseNoList)) {
            throw new BizException("未找到城配仓对应的仓库映射");
        }
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryListByWarehouseNos(warehouseNoList);
        if (CollectionUtils.isEmpty(warehouseStorageEntityList)) {
            throw new BizException("未找到城配仓对应的仓库信息");
        }

        // 查询已有自定义围栏信息
        WncFenceChangeRecordsQueryParam wncFenceChangeRecordsQueryParam = new WncFenceChangeRecordsQueryParam();
        wncFenceChangeRecordsQueryParam.setChangeBatchNo(changeBatchNo);
        wncFenceChangeRecordsQueryParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        List<WncFenceChangeRecordsEntity> wncFenceChangeRecordsEntityList = wncFenceChangeRecordsQueryRepository.selectByCondition(wncFenceChangeRecordsQueryParam);
        List<Integer> storeNoList = wncFenceChangeRecordsEntityList.stream().map(WncFenceChangeRecordsEntity::getFenceStoreNo).distinct().collect(Collectors.toList());
        Map<Integer, List<Integer>> storeWarehouseMap = warehouseLogisticsMappingRepository.queryLogisticsMapping(storeNoList);
        List<Integer> existWarehouseNoList = storeWarehouseMap.get(param.getStoreNo());
        if (!CollectionUtils.isEmpty(existWarehouseNoList)) {
            // 新增后库存仓是否与已存在库存仓完全一致
            if (!new HashSet<>(warehouseNoList).containsAll(existWarehouseNoList)) {
                throw new BizException("新增后库存仓与已存在库存仓不一致");
            }
        }

        // 变更后的围栏记录实体
        WncFenceChangeRecordsCommandParam fenceChangeParam = new WncFenceChangeRecordsCommandParam();
        fenceChangeParam.setChangeBatchNo(changeBatchNo);
        fenceChangeParam.setFenceName(param.getFenceName());
        fenceChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        fenceChangeParam.setFenceStoreNo(param.getStoreNo());
        fenceChangeParam.setFenceStoreName(param.getStoreName());
        fenceChangeParam.setFenceAreaNo(param.getAreaNo());
        fenceChangeParam.setFenceAreaName(param.getAreaName());
        fenceChangeParam.setFenceWarehouseNos(Joiner.on(",").join(warehouseNoList));
        fenceChangeParam.setFenceWarehouseNames(Joiner.on(",").join(warehouseStorageEntityList.stream().map(WarehouseStorageEntity::getWarehouseName).collect(Collectors.toList())));
        // 围栏实体
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setFenceName(param.getFenceName());
        fenceEntity.setStoreNo(param.getStoreNo());
        fenceEntity.setStoreName(param.getStoreName());
        fenceEntity.setAreaNo(param.getAreaNo());
        fenceEntity.setAreaName(param.getAreaName());
        fenceEntity.setType(FenceEnums.Type.CUSTOM);
        fenceEntity.setStatus(FenceEnums.Status.VALID.getValue().equals(param.getFenceStatus()) ? FenceEnums.Status.VALID : FenceEnums.Status.STOP);
        // 配送周期
        FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
        fenceDeliveryEntity.setDeliveryFrequent(param.getFenceDeliveryCommandParam().getDeliveryFrequent());
        fenceDeliveryEntity.setNextDeliveryDate(param.getFenceDeliveryCommandParam().getNextDeliveryDate());
        fenceDeliveryEntity.setBeginCalculateDate(param.getFenceDeliveryCommandParam().getBeginCalculateDate());
        fenceDeliveryEntity.setFrequentMethod(param.getFenceDeliveryCommandParam().getFrequentMethod());
        fenceDeliveryEntity.setDeliveryFrequentInterval(param.getFenceDeliveryCommandParam().getDeliveryFrequentInterval());
        fenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
        // 下单渠道白名单配置
        List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = param.getFenceChannelBusinessWhiteConfigEntityList();
        fenceEntity.setFenceChannelBusinessWhiteConfigEntities(fenceChannelBusinessWhiteConfigEntities);
        fenceEntity.setOrderChannelType(fenceChannelBusinessWhiteConfigEntities.stream()
                .map(FenceChannelBusinessWhiteConfigEntity::getOrderChannelType)
                .distinct().collect(Collectors.joining(",")));
        fenceChangeParam.setFenceMsg(JSON.toJSONString(fenceEntity));
        // 新增变更后围栏变更记录
        WncFenceChangeRecordsEntity afterChangeRecord = wncFenceChangeRecordsCommandRepository.insertSelective(fenceChangeParam);
        if (null == afterChangeRecord || null == afterChangeRecord.getId()) {
            throw new BizException("新增变更后围栏变更记录失败");
        }

        return afterChangeRecord.getId();
    }

    /**
     * 新增自定义围栏区域变更记录
     *
     * @param fenceChangeRecordsId 围栏变更记录ID
     * @param customAreaName       自定义区域名称
     * @param geoShape             坐标POI图形
     * @param province             省
     * @param city                 市
     * @param area                 区
     * @param areaDrawType         区域绘制类型
     * @param adCode
     * @return 围栏区域变更记录ID
     */
    public Long addCustomFenceAreaChangeRecord(Long fenceChangeRecordsId, String customAreaName, String geoShape,
                                               String province, String city, String area, Integer areaDrawType, String adCode) {
        AdCodeMsgEntity adCodeMsgDetail = new AdCodeMsgEntity();
        adCodeMsgDetail.setProvince(province);
        adCodeMsgDetail.setCity(city);
        adCodeMsgDetail.setArea(area);
        adCodeMsgDetail.setCustomAreaName(customAreaName);
        adCodeMsgDetail.setAreaType(AdCodeMsgEnums.AreaType.CUSTOM_AREA.getValue());
        adCodeMsgDetail.setAreaDrawType(areaDrawType);
        adCodeMsgDetail.setAdCode(adCode);

        // 新增自定义围栏区域变更记录
        WncFenceAreaChangeRecordsCommandParam areaChangeParam = new WncFenceAreaChangeRecordsCommandParam();
        areaChangeParam.setFenceChangeId(fenceChangeRecordsId);
        areaChangeParam.setCustomAreaName(customAreaName);
        areaChangeParam.setGeoShape(geoShape);
        areaChangeParam.setCity(city);
        areaChangeParam.setArea(area);
        areaChangeParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        areaChangeParam.setAdCodeMsgDetail(JSON.toJSONString(adCodeMsgDetail));
        areaChangeParam.setAreaDrawType(areaDrawType);

        WncFenceAreaChangeRecordsEntity savedEntity = wncFenceAreaChangeRecordsCommandRepository.insertSelective(areaChangeParam);
        return savedEntity.getId();
    }

    /**
     * 更新自定义围栏变更记录
     *
     * @param param 更新参数
     * @return 是否成功
     */
    public boolean updateCustomFenceChangeRecord(UpdateCustomFenceChangeRecordCommandParam param) {
        // 查询现有的围栏变更记录
        WncFenceChangeRecordsEntity existingRecord = wncFenceChangeRecordsQueryRepository.selectById(param.getFenceChangeRecordsId());
        if (existingRecord == null) {
            throw new BizException("围栏变更记录不存在");
        }

        // 根据传入参数组装FenceEntity
        FenceEntity fenceEntity = new FenceEntity();
        fenceEntity.setFenceName(!StringUtils.isBlank(param.getFenceName()) ? param.getFenceName() : null != existingRecord.getFenceDetailEntity() ? existingRecord.getFenceDetailEntity().getFenceName() : null);
        fenceEntity.setStoreNo(null != param.getStoreNo() ? param.getStoreNo() : null != existingRecord.getFenceDetailEntity() ? existingRecord.getFenceDetailEntity().getStoreNo() : null);
        fenceEntity.setStoreName(!StringUtils.isBlank(param.getStoreName()) ? param.getStoreName() : null != existingRecord.getFenceDetailEntity() ? existingRecord.getFenceDetailEntity().getStoreName() : null);
        fenceEntity.setAreaNo(null != param.getAreaNo() ? param.getAreaNo() : null != existingRecord.getFenceDetailEntity() ? existingRecord.getFenceDetailEntity().getAreaNo() : null);
        fenceEntity.setAreaName(!StringUtils.isBlank(param.getAreaName()) ? param.getAreaName() : null != existingRecord.getFenceDetailEntity() ? existingRecord.getFenceDetailEntity().getAreaName() : null);
        fenceEntity.setType(param.getType() != null ?
                (param.getType() == 2 ? net.summerfarm.wnc.common.enums.FenceEnums.Type.CUSTOM :
                        net.summerfarm.wnc.common.enums.FenceEnums.Type.NEW_BUILD) :
                net.summerfarm.wnc.common.enums.FenceEnums.Type.CUSTOM);
        fenceEntity.setStatus(param.getFenceStatus() != null ?
                (param.getFenceStatus() == 0 ? FenceEnums.Status.VALID : FenceEnums.Status.STOP) :
                FenceEnums.Status.VALID);

        // 配送周期
        if (param.getFenceDeliveryCommandParam() != null) {
            FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
            fenceDeliveryEntity.setDeliveryFrequent(param.getFenceDeliveryCommandParam().getDeliveryFrequent());
            fenceDeliveryEntity.setNextDeliveryDate(param.getFenceDeliveryCommandParam().getNextDeliveryDate());
            fenceDeliveryEntity.setBeginCalculateDate(param.getFenceDeliveryCommandParam().getBeginCalculateDate());
            fenceDeliveryEntity.setFrequentMethod(param.getFenceDeliveryCommandParam().getFrequentMethod());
            fenceDeliveryEntity.setDeliveryFrequentInterval(param.getFenceDeliveryCommandParam().getDeliveryFrequentInterval());
            fenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
        } else {
            FenceDeliveryEntity fenceDeliveryEntity = new FenceDeliveryEntity();
            FenceEntity existingFenceEntity = existingRecord.getFenceDetailEntity();
            if (null != existingFenceEntity) {
                FenceDeliveryEntity existingFenceDeliveryEntity = existingFenceEntity.getFenceDeliveryEntity();
                if (null != existingFenceDeliveryEntity) {
                    fenceDeliveryEntity.setDeliveryFrequent(existingFenceDeliveryEntity.getDeliveryFrequent());
                    fenceDeliveryEntity.setNextDeliveryDate(existingFenceDeliveryEntity.getNextDeliveryDate());
                    fenceDeliveryEntity.setBeginCalculateDate(existingFenceDeliveryEntity.getBeginCalculateDate());
                    fenceDeliveryEntity.setFrequentMethod(existingFenceDeliveryEntity.getFrequentMethod());
                    fenceDeliveryEntity.setDeliveryFrequentInterval(existingFenceDeliveryEntity.getDeliveryFrequentInterval());
                    fenceEntity.setFenceDeliveryEntity(fenceDeliveryEntity);
                }
            }
        }

        // 下单渠道白名单配置
        if (!CollectionUtils.isEmpty(param.getFenceChannelBusinessWhiteConfigEntityList())) {
            fenceEntity.setFenceChannelBusinessWhiteConfigEntities(param.getFenceChannelBusinessWhiteConfigEntityList());
        } else {
            FenceEntity existingFenceEntity = existingRecord.getFenceDetailEntity();
            if (null != existingFenceEntity) {
                fenceEntity.setFenceChannelBusinessWhiteConfigEntities(existingFenceEntity.getFenceChannelBusinessWhiteConfigEntities());
            }
        }

        // 更新围栏变更记录
        WncFenceChangeRecordsCommandParam updateParam = new WncFenceChangeRecordsCommandParam();
        updateParam.setId(param.getFenceChangeRecordsId());
        updateParam.setFenceMsg(JSON.toJSONString(fenceEntity));
        updateParam.setFenceStoreNo(param.getStoreNo());
        updateParam.setFenceAreaNo(param.getAreaNo());

        return wncFenceChangeRecordsCommandRepository.updateSelectiveById(updateParam) > 0;
    }

    /**
     * 更新自定义围栏区域变更记录
     * @param fenceAreaChangeRecordsId 围栏区域变更记录ID
     * @param customAreaName 自定义区域名称
     * @param geoShape 坐标POI图形
     * @param fenceChangeRecordsId 围栏变更记录ID
     * @return 是否成功
     */
    public boolean updateCustomFenceAreaChangeRecord(Long fenceAreaChangeRecordsId, String customAreaName, Long fenceChangeRecordsId, String geoShape) {
        // 查询现有的围栏区域变更记录
        WncFenceAreaChangeRecordsEntity existingRecord = wncFenceAreaChangeRecordsQueryRepository.selectById(fenceAreaChangeRecordsId);
        if (existingRecord == null) {
            throw new BizException("围栏区域变更记录不存在");
        }

        // 构建更新参数
        WncFenceAreaChangeRecordsCommandParam updateParam = new WncFenceAreaChangeRecordsCommandParam();
        updateParam.setId(fenceAreaChangeRecordsId);
        updateParam.setCustomAreaName(customAreaName);
        updateParam.setFenceChangeId(fenceChangeRecordsId);
        updateParam.setGeoShape(geoShape);

        return wncFenceAreaChangeRecordsCommandRepository.updateSelectiveById(updateParam) > 0;
    }
}

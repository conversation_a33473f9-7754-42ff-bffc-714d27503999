package net.summerfarm.wnc.domain.fence.repository;



import com.github.pagehelper.PageInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;



/**
*
* <AUTHOR>
* @date 2025-08-28 15:03:54
* @version 1.0
*
*/
public interface WncCityAreaChangeWarehouseRecordsQueryRepository {

    PageInfo<WncCityAreaChangeWarehouseRecordsEntity> getPage(WncCityAreaChangeWarehouseRecordsQueryParam param);

    WncCityAreaChangeWarehouseRecordsEntity selectById(Long id);

    List<WncCityAreaChangeWarehouseRecordsEntity> selectByCondition(WncCityAreaChangeWarehouseRecordsQueryParam param);

    /**
     * 根据切仓任务ID集合查询城市区域切仓记录
     * @param fenceChangeTaskIds 切仓任务ID集合
     * @return 城市区域切仓记录集合
     */
    Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> selectTaskIdMapByFenceChangeTaskIdsChangeStatus(List<Long> fenceChangeTaskIds, Integer changeStatus);

    /**
     * 查询可执行的切仓记录
     *
     * @param preExeTime         预约执行时间
     * @param changeStatus       变更状态
     * @param areaDefinationType
     * @return 切仓记录集合
     */
    List<WncCityAreaChangeWarehouseRecordsEntity> selectExecutableRecords(LocalDateTime preExeTime, Integer changeStatus, Integer areaDefinationType);

    /**
     * 根据城市区域查询正在生效的城市区域切仓记录
     * @param city 城市
     * @param areas 区域
     * @param changeStatus 变更状态
     * @return 切仓记录
     */
    List<WncCityAreaChangeWarehouseRecordsEntity> selectByCityAreasChangeStage(String city, List<String> areas, WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus changeStatus);
}
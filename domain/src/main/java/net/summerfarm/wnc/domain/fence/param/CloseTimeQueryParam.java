package net.summerfarm.wnc.domain.fence.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.client.enums.SourceEnum;

/**
 * Description:截单时间查询参数 <br/>
 * date: 2024/4/17 16:00<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CloseTimeQueryParam {

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 订单来源不能为空
     */
    private SourceEnum source;

    /**
     * 联系人ID
     */
    private Long contactId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 是否是鲜沐映射的POP品类型 true 鲜沐POP品 false纯POP品
     */
    private Boolean isXmPopSkuType;

    /**
     * poi
     */
    private String poi;


}

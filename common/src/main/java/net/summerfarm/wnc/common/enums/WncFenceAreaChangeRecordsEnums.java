package net.summerfarm.wnc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 围栏变更执行记录枚举
 * date: 2025/8/28 17:14<br/>
 *
 * <AUTHOR> />
 */
public interface WncFenceAreaChangeRecordsEnums {

    // 围栏变更阶段 0变更前 1变更后 2结束时
    @Getter
    @AllArgsConstructor
    enum FenceChangeStage{
        BEFORE(0, "变更前"),
        AFTER(1, "变更后"),
        END(2, "结束时"),
        ;
        private Integer value;
        private String content;

    }

    // 区域绘制类型 0已绘制 1未绘制（其他区域）
    @Getter
    @AllArgsConstructor
    enum AreaDrawType{
        DRAW(0, "已绘制"),
        UN_DRAW(1, "未绘制"),
        ;
        private Integer value;
        private String content;

    }

}

package net.summerfarm.wnc.common.query.fence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.wnc.client.enums.AreaTypeEnum;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/17 10:42<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdCodeMsgQuery extends BasePageInput {
    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 区域ID集合
     */
    private List<Integer> areaIds;

    /**
     * 围栏ID
     */
    private Integer fenceId;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    private Integer status;

    /**
     * 状态 0 正常 1 失效 3停用
     */
    private List<Integer> statusList;

    /**
     * 围栏ID集合
     */
    private List<Integer> fenceIds;


    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 区域编码集合
     */
    private List<String> adCodeList;

    /**
     * 城配仓编号
     */
    private List<Integer> storeNos;

    /**
     * 城配仓状态
     */
    private Integer storeStatus;

    /**
     * 围栏状态
     */
    private Integer fenceStatus;

    /**
     * 区域绘制类型
     */
    private Integer areaDrawType;

    /**
     * 区域名称集合
     */
    private List<String> areaList;

    /**
     * 区域类型 0省市区区域 1自定义区域
     */
    private Integer areaType;
}

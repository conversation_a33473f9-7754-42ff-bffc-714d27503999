package net.summerfarm.wnc.common.query.fence;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * Description: 围栏分页查询实体
 * date: 2023/10/26 18:57
 *
 * <AUTHOR>
 */
@Data
public class FencePageQuery extends BasePageInput {

    private static final long serialVersionUID = -7471593955380753684L;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 运营服务区编号
     */
    private Integer areaNo;

    /**
     * 行政城市
     */
    private String cityName;

    /**
     * 类型，0：新建围栏，1：围栏拆分
     */
    private Integer type;

    /**
     * 状态，0：正常，3：暂停
     *
     */
    private Integer status;

    /**
     * 状态集合
     */
    private List<Integer> statusList;

    /**
     * 类型，0：新建围栏，1：围栏拆分
     */
    private List<Integer> types;
}

package net.summerfarm.wnc.common.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 多边形环方向工具类（支持字符串输入）
 */
public class PolygonRingUtils {

    // 正则表达式匹配坐标点
    private static final Pattern POINT_PATTERN = Pattern.compile("\\s*\\[\\s*(-?\\d+\\.?\\d*)\\s*,\\s*(-?\\d+\\.?\\d*)\\s*\\]\\s*,?\\s*");
    /**
     * 从字符串解析多边形环
     * @param ringStr 格式："[[x1,y1],[x2,y2],...,[xn,yn]]"
     * @return 多边形环坐标列表
     */
    public static List<double[]> parseRing(String ringStr) {
        if (ringStr == null || ringStr.trim().isEmpty()) {
            throw new IllegalArgumentException("Ring string cannot be null or empty");
        }

        List<double[]> ring = new ArrayList<>();
        Matcher matcher = POINT_PATTERN.matcher(ringStr);

        while (matcher.find()) {
            try {
                double x = Double.parseDouble(matcher.group(1).trim());
                double y = Double.parseDouble(matcher.group(2).trim());
                ring.add(new double[]{x, y});
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid coordinate format: " + matcher.group());
            }
        }

        validateRing(ring);
        return ring;
    }

    /**
     * 将多边形环转换为字符串
     * @param ring 多边形环坐标列表
     * @return 字符串格式："[[x1,y1],[x2,y2],...,[xn,yn]]"
     */
    public static String ringToString(List<double[]> ring) {
        StringBuilder sb = new StringBuilder("[");
        for (double[] point : ring) {
            sb.append(String.format("[%.6f,%.6f],", point[0], point[1]));
        }
        // 移除最后一个逗号
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 1);
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 判断多边形环是顺时针还是逆时针
     * @param ringStr 多边形环字符串
     * @return true=逆时针（CCW），false=顺时针（CW）
     */
    public static boolean isCounterClockwise(String ringStr) {
        return isCounterClockwise(parseRing(ringStr));
    }

    /**
     * 反转多边形环的方向
     * @param ringStr 多边形环字符串
     * @return 反转后的多边形环字符串
     */
    public static String reverseRing(String ringStr) {
        return ringToString(reverseRing(parseRing(ringStr)));
    }

    /**
     * 确保多边形环是逆时针方向
     * @param ringStr 多边形环字符串
     * @return 逆时针方向的多边形环字符串
     */
    public static String ensureCounterClockwise(String ringStr) {
        return ringToString(ensureCounterClockwise(parseRing(ringStr)));
    }

    /**
     * 确保多边形环是顺时针方向
     * @param ringStr 多边形环字符串
     * @return 顺时针方向的多边形环字符串
     */
    public static String ensureClockwise(String ringStr) {
        return ringToString(ensureClockwise(parseRing(ringStr)));
    }

    // 以下是原有的内部方法（保持不变）
    private static boolean isCounterClockwise(List<double[]> ring) {
        validateRing(ring);
        double area = calculateSignedArea(ring);
        return area < 0;
    }

    private static List<double[]> reverseRing(List<double[]> ring) {
        validateRing(ring);
        List<double[]> reversed = new ArrayList<>(ring);
        Collections.reverse(reversed);
        return reversed;
    }

    private static List<double[]> ensureCounterClockwise(List<double[]> ring) {
        if (!isCounterClockwise(ring)) {
            return reverseRing(ring);
        }
        return new ArrayList<>(ring);
    }

    private static List<double[]> ensureClockwise(List<double[]> ring) {
        if (isCounterClockwise(ring)) {
            return reverseRing(ring);
        }
        return new ArrayList<>(ring);
    }

    private static double calculateSignedArea(List<double[]> ring) {
        double area = 0.0;
        int n = ring.size();
        for (int i = 0; i < n; i++) {
            double[] current = ring.get(i);
            double[] next = ring.get((i + 1) % n);
            area += (next[0] - current[0]) * (next[1] + current[1]);
        }
        return area;
    }

    private static void validateRing(List<double[]> ring) {
        if (ring == null || ring.size() < 3) {
            throw new IllegalArgumentException("Ring must have at least 3 points");
        }
        for (double[] point : ring) {
            if (point == null || point.length != 2) {
                throw new IllegalArgumentException("Each point must have exactly 2 coordinates (x,y)");
            }
        }
    }

    public static void main(String[] args) {
        String ringStr = "[[120.044792,30.241065],[120.044306,30.240478],[120.044271,30.239132],[120.044792,30.241065]]";

        System.out.println("原始环: " + ringStr);
        System.out.println("是否逆时针: " + isCounterClockwise(ringStr));
        
        String reversed = reverseRing(ringStr);
        System.out.println("\n反转后的环: " + reversed);
        System.out.println("是否逆时针: " + isCounterClockwise(reversed));
        
        String ensuredCCW = ensureCounterClockwise(ringStr);
        System.out.println("\n确保逆时针的环: " + ensuredCCW);
        
        String ensuredCW = ensureClockwise(ringStr);
        System.out.println("\n确保顺时针的环: " + ensuredCW);
    }
}
package net.summerfarm.wnc.config.inbound.scheduler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.wnc.application.inbound.scheduler.changeTask.NormalFenceChangeAreaHandleJob;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * Description: <br/>
 * date: 2025/9/12 18:06<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class NormalFenceChangeAreaHandleJobTest {

    @Resource
    private NormalFenceChangeAreaHandleJob normalFenceChangeAreaHandleJob;

    @Test
    public void normalFenceChangeAreaHandleJobTest() throws Exception {
        ProcessResult processResult = normalFenceChangeAreaHandleJob.processResult(null);
    }
}

package net.summerfarm.wnc.config.inbound.scheduler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.wnc.application.inbound.scheduler.changeTask.CustomFenceChangeAreaHandleJob;
import net.summerfarm.wnc.domain.fence.entity.CustomFenceAreaEsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceAreaChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsCommandRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.CustomFenceAreaEsCommandDomainService;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2025/9/16 11:04<br/>
 *
 * <AUTHOR> />
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CustomFenceChangeAreaHandleJobTest {
    @Resource
    private CustomFenceChangeAreaHandleJob customFenceChangeAreaHandleJob;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Resource
    private CustomFenceAreaEsCommandRepository customFenceAreaEsCommandRepository;

    @Test
    public void customFenceChangeAreaHandleJobTest() throws Exception {
        ProcessResult processResult = customFenceChangeAreaHandleJob.processResult(null);
    }

    @Test
    public void saveEs() throws Exception {
        // 查询已绘制区域
        WncFenceAreaChangeRecordsQueryParam param = new WncFenceAreaChangeRecordsQueryParam();
        param.setFenceChangeIds(Arrays.asList(46L));
        param.setAreaDrawType(1);
        List<WncFenceAreaChangeRecordsEntity> wncFenceAreaChangeRecordsEntities = wncFenceAreaChangeRecordsQueryRepository.selectByCondition(param);
        if (CollectionUtils.isEmpty(wncFenceAreaChangeRecordsEntities)) {
            return;
        }

        List<CustomFenceAreaEsEntity> customFenceAreaEsEntities = wncFenceAreaChangeRecordsEntities.stream().map(e -> {
            CustomFenceAreaEsEntity customFenceAreaEsEntity = new CustomFenceAreaEsEntity();

            customFenceAreaEsEntity.setId(String.valueOf(e.getId()));
            customFenceAreaEsEntity.setAdCodeMsgId(e.getAdCodeMsgId());
            customFenceAreaEsEntity.setProvince(e.getAdCodeMsgDetailEntity().getProvince());
            customFenceAreaEsEntity.setCity(e.getCity());
            customFenceAreaEsEntity.setArea(e.getArea());
            customFenceAreaEsEntity.setGeoShape(e.getGeoShape());
            customFenceAreaEsEntity.setStatus(e.getAdCodeMsgDetailEntity().getStatus());

            return customFenceAreaEsEntity;
        }).collect(Collectors.toList());

        customFenceAreaEsCommandRepository.saveAll(customFenceAreaEsEntities);
    }
}

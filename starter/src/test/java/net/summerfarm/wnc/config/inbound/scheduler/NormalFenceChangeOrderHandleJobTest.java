package net.summerfarm.wnc.config.inbound.scheduler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.wnc.application.inbound.scheduler.changeTask.NormalFenceChangeOrderHandleJob;
import net.summerfarm.wnc.starter.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class NormalFenceChangeOrderHandleJobTest {

    @Resource
    private NormalFenceChangeOrderHandleJob normalFenceChangeOrderHandleJob;

    @Test
    public void normalFenceChangeOrderHandleJobTest() throws Exception {
        ProcessResult processResult = normalFenceChangeOrderHandleJob.processResult(null);
    }
}

<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>net.summerfarm</groupId>
    <artifactId>summerfarm-wnc</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <app.version>1.0.0-SNAPSHOT</app.version>
        <app.client.version>1.4.5-CXH-SNAPSHOT</app.client.version>
        <warehouse.version>1.2.4</warehouse.version>
        <xianmu-common.version>1.1.7-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.9</xianmu-dubbo.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-task.version>1.0.5</xianmu-task.version>
        <xianmu-oss.version>1.0.7</xianmu-oss.version>
        <xianmu-mq.version>1.2.1</xianmu-mq.version>
        <summerfarm-common.version>1.5.10-RELEASE</summerfarm-common.version>
        <xianmu-redis.version>1.0.1</xianmu-redis.version>


        <fastjson.version>2.0.51</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <page.version>1.2.7</page.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <rocketmq.starter.version>2.1.1</rocketmq.starter.version>
        <redisson.version>3.11.1</redisson.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <cosfo-manage-client.version>1.1.0-RELEASE</cosfo-manage-client.version>
        <tms-client.version>1.0.6</tms-client.version>
        <authentication-client.version>1.1.5</authentication-client.version>
        <authentication-sdk.version>1.1.2</authentication-sdk.version>
        <auth.version>2.0.0</auth.version>
        <hutool.version>5.8.4</hutool.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
        <wms-client.version>1.6.4-RELEASE</wms-client.version>
        <ofc-client.version>1.6.9-hs-fence-SNAPSHOT</ofc-client.version>
        <goods-center-client.version>1.2.0-RELEASE</goods-center-client.version>
        <sf-mall-manage-client.version>1.0.8-RELEASE</sf-mall-manage-client.version>
        <usercenter-client.version>1.1.0</usercenter-client.version>
        <xianmu-mybatis-interceptor-support.version>1.0.6-RELEASE</xianmu-mybatis-interceptor-support.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <easy-es.version>1.1.1</easy-es.version>
        <pagehelper.version>5.3.2</pagehelper.version>
        <sentinel.version>1.0.3-RELEASE</sentinel.version>
        <gt-geojson.version>24.2-oss84-1</gt-geojson.version>
    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-facade</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-api</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-domain</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-inbound</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-starter</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-wnc-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>xianmu-download-support</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>common-client</artifactId>
                <version>1.0.15-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-oss-support</artifactId>
                <version>${xianmu-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-authorization</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-robot-util</artifactId>
                <version>${xianmu-robot-util.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm.wms</groupId>
                <artifactId>summerfarm-wms-client</artifactId>
                <version>${wms-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>ofc-client</artifactId>
                <version>${ofc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>summerfarm-common</artifactId>
                <version>${summerfarm-common.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>rocketmq-spring-boot-starter</artifactId>
                        <groupId>org.apache.rocketmq</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goods-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>sf-mall-manage-client</artifactId>
                <version>${sf-mall-manage-client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-redis-support</artifactId>
                <version>${xianmu-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-mybatis-interceptor-support</artifactId>
                <version>${xianmu-mybatis-interceptor-support.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>summerfarm-inventory-client</artifactId>
                <version>2.0.22-RELEASE</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${page.version}</version>
            </dependency>
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <!-- rocket mq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.starter.version}</version>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.version}</version>
            </dependency>
            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>tms-client</artifactId>
                <version>${tms-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>summerfarm-authorization</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-sdk</artifactId>
                <version>${authentication-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>${authentication-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.summerfarm</groupId>
                        <artifactId>summerfarm-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- POJO转换工具 begin -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- POJO转换工具 end -->

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- elasticsearch -->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-es.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-sentinel-support</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>io.oss84.geotools</groupId>
                <artifactId>gt-geojson</artifactId>
                <version>${gt-geojson.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>api</module>
        <module>application</module>
        <module>inbound</module>
        <module>domain</module>
        <module>infrastructure</module>
        <module>common</module>
        <module>facade</module>
        <module>starter</module>
    </modules>
</project>

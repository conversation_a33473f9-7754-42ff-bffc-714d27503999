package net.summerfarm.wnc.application.service.fence.impl;

import net.summerfarm.wnc.api.fence.input.FenceChannelBusinessWhiteConfigCommandInput;
import net.summerfarm.wnc.application.fence.converter.FenceEntityConverter;
import net.summerfarm.wnc.application.inbound.controller.fence.Input.command.*;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.AddCustomFenceAreaVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.AddCustomFenceChangeRecordVO;
import net.summerfarm.wnc.application.inbound.controller.fence.vo.CityAreaChangeWarehouseRecordAddVO;
import net.summerfarm.wnc.application.service.fence.CustomFenceCommandService;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.AddCustomFenceChangeRecordCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.FenceChannelBusinessWhiteConfigCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.FenceDeliveryCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.UpdateCustomFenceChangeRecordCommandParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.service.WncFenceAreaChangeRecordsCommandDomainService;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsCommandDomainService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2025/8/26 17:30<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CustomFenceCommandServiceImpl implements CustomFenceCommandService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsCommandDomainService wncCityAreaChangeWarehouseRecordsCommandDomainService;

    @Resource
    private WncFenceAreaChangeRecordsCommandDomainService wncFenceAreaChangeRecordsCommandDomainService;

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;

    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;

    @Resource
    private WncFenceChangeRecordsCommandDomainService wncFenceChangeRecordsCommandDomainService;

    @Override
    public CityAreaChangeWarehouseRecordAddVO addCityAreaChangeWarehouseRecord(AddCityAreaChangeWarehouseRecordCommandInput input) {
        Long recordId = wncCityAreaChangeWarehouseRecordsCommandDomainService.addCityAreaChangeWarehouseRecord(
                input.getProvince(), input.getCity(), input.getArea());

        CityAreaChangeWarehouseRecordAddVO result = new CityAreaChangeWarehouseRecordAddVO();
        result.setCityAreaChangeWarehouseRecordId(recordId);
        return result;
    }

    @Override
    public AddCustomFenceChangeRecordVO addCustomFenceChangeRecord(AddCustomFenceChangeRecordCommandInput input) {
        // 构建参数
        AddCustomFenceChangeRecordCommandParam param = new AddCustomFenceChangeRecordCommandParam();
        param.setCityAreaChangeWarehouseRecordId(input.getCityAreaChangeWarehouseRecordId());
        param.setFenceName(input.getFenceName());
        param.setStoreNo(input.getStoreNo());
        param.setStoreName(input.getStoreName());
        param.setAreaNo(input.getAreaNo());
        param.setAreaName(input.getAreaName());
        param.setType(input.getType());
        param.setFenceStatus(input.getFenceStatus());
        FenceDeliveryCommandParam fenceDeliveryCommandParam = new FenceDeliveryCommandParam();
        fenceDeliveryCommandParam.setDeliveryFrequent(input.getFenceDeliveryCommand().getDeliveryFrequent());
        fenceDeliveryCommandParam.setFrequentMethod(input.getFenceDeliveryCommand().getFrequentMethod());
        fenceDeliveryCommandParam.setBeginCalculateDate(input.getFenceDeliveryCommand().getBeginCalculateDate());
        fenceDeliveryCommandParam.setDeliveryFrequentInterval(input.getFenceDeliveryCommand().getDeliveryFrequentInterval());
        fenceDeliveryCommandParam.setNextDeliveryDate(input.getFenceDeliveryCommand().getNextDeliveryDate());
        param.setFenceDeliveryCommandParam(fenceDeliveryCommandParam);
        List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntityList = FenceEntityConverter.channelBusCommand2EntityList(input.getFenceChannelBusinessWhiteConfigCommandInputList());
        param.setFenceChannelBusinessWhiteConfigEntityList(fenceChannelBusinessWhiteConfigEntityList);

        // 新增围栏变更记录
        Long fenceChangeRecordsId = wncFenceAreaChangeRecordsCommandDomainService.addCustomFenceChangeRecord(param);

        AddCustomFenceChangeRecordVO result = new AddCustomFenceChangeRecordVO();
        result.setFenceChangeRecordsId(fenceChangeRecordsId);
        return result;
    }

    @Override
    public AddCustomFenceAreaVO addCustomFenceArea(AddCustomFenceAreaCommandInput input) {
        WncCityAreaChangeWarehouseRecordsEntity wncCityAreaChangeWarehouseRecordsEntity = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());
        if (wncCityAreaChangeWarehouseRecordsEntity == null) {
            throw new RuntimeException("城市区域变更记录不存在");
        }

        Long fenceAreaChangeRecordsId = wncFenceAreaChangeRecordsCommandDomainService.addCustomFenceAreaChangeRecord(
                input.getFenceChangeRecordsId(),
                input.getCustomAreaName(),
                input.getGeoShape(),
                wncCityAreaChangeWarehouseRecordsEntity.getProvince(),
                wncCityAreaChangeWarehouseRecordsEntity.getCity(),
                wncCityAreaChangeWarehouseRecordsEntity.getArea(),
                input.getAreaDrawType(),
                input.getAdCode()
        );

        AddCustomFenceAreaVO result = new AddCustomFenceAreaVO();
        result.setFenceAreaChangeRecordsId(fenceAreaChangeRecordsId);
        return result;
    }

    @Override
    public boolean updateCustomFenceChangeRecord(UpdateCustomFenceChangeRecordCommandInput input) {
        // 转换Input为Param
        UpdateCustomFenceChangeRecordCommandParam param = new UpdateCustomFenceChangeRecordCommandParam();
        param.setFenceChangeRecordsId(input.getFenceChangeRecordsId());
        param.setFenceName(input.getFenceName());
        param.setStoreNo(input.getStoreNo());
        param.setStoreName(input.getStoreName());
        param.setAreaNo(input.getAreaNo());
        param.setAreaName(input.getAreaName());
        param.setType(input.getType());
        param.setFenceStatus(input.getFenceStatus());
        param.setOrderChannelType(input.getOrderChannelType());

        // 转换配送周期
        if (input.getFenceDeliveryCommand() != null) {
            FenceDeliveryCommandParam fenceDeliveryCommandParam = new FenceDeliveryCommandParam();
            fenceDeliveryCommandParam.setNextDeliveryDate(input.getFenceDeliveryCommand().getNextDeliveryDate());
            fenceDeliveryCommandParam.setFrequentMethod(input.getFenceDeliveryCommand().getFrequentMethod());
            fenceDeliveryCommandParam.setDeliveryFrequent(input.getFenceDeliveryCommand().getDeliveryFrequent());
            fenceDeliveryCommandParam.setBeginCalculateDate(input.getFenceDeliveryCommand().getBeginCalculateDate());
            fenceDeliveryCommandParam.setDeliveryFrequentInterval(input.getFenceDeliveryCommand().getDeliveryFrequentInterval());
            param.setFenceDeliveryCommandParam(fenceDeliveryCommandParam);
        }

        // 转换渠道白名单配置
        if (!CollectionUtils.isEmpty(input.getFenceChannelBusinessWhiteConfigCommandInputList())) {
            List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntityList = FenceEntityConverter.channelBusCommand2EntityList(input.getFenceChannelBusinessWhiteConfigCommandInputList());
            param.setFenceChannelBusinessWhiteConfigEntityList(fenceChannelBusinessWhiteConfigEntityList);
        }

        return wncFenceAreaChangeRecordsCommandDomainService.updateCustomFenceChangeRecord(param);
    }

    @Override
    public boolean updateCustomFenceArea(UpdateCustomFenceAreaCommandInput input) {
        // 查询运营区域
        WncCityAreaChangeWarehouseRecordsEntity wncCityAreaChangeWarehouseRecordsEntity = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());
        if (wncCityAreaChangeWarehouseRecordsEntity == null) {
            throw new RuntimeException("城市区域变更记录不存在");
        }
        // 只有等待生效状态才能修改
        if (!WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue().equals(wncCityAreaChangeWarehouseRecordsEntity.getChangeStatus())) {
            throw new RuntimeException("只有等待生效状态才能修改");
        }


        // 调用领域服务更新自定义围栏区域变更记录
        return wncFenceAreaChangeRecordsCommandDomainService.updateCustomFenceAreaChangeRecord(
                input.getFenceAreaChangeRecordsId(),
                input.getCustomAreaName(),
                input.getFenceChangeRecordsId(),
                input.getGeoShape()
        );
    }

    @Override
    public boolean deleteCustomFenceChangeRecord(DelCustomFenceChangeRecordCommandInput input) {
        // 1. 查询行政区域变更记录
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());
        if (cityAreaRecord == null) {
            throw new BizException("城市区域变更记录不存在");
        }

        // 2. 检查行政区域变更记录的状态是否为等待生效状态
        if (!WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue().equals(cityAreaRecord.getChangeStatus())) {
            throw new BizException("只有等待生效状态下才可删除");
        }

        // 3. 查询当前围栏的区域变更记录
        List<WncFenceAreaChangeRecordsEntity> areaChangeRecords = wncFenceAreaChangeRecordsQueryRepository.selectByFenceChangeIds(
                Collections.singletonList(input.getFenceChangeRecordsId())
        );

        if (!CollectionUtils.isEmpty(areaChangeRecords)) {
            throw new BizException("当前围栏已关联自定义区域，无法删除");
        }

        // 4. 执行删除操作 - 删除围栏变更记录
        wncFenceChangeRecordsCommandDomainService.delete(input.getFenceChangeRecordsId());

        return true;
    }

    @Override
    public boolean deleteCustomFenceAreaChangeRecord(DelCustomFenceAreaChangeRecordCommandInput input) {
        // 1. 查询行政区域变更记录
        WncCityAreaChangeWarehouseRecordsEntity cityAreaRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(input.getCityAreaChangeWarehouseRecordId());
        if (cityAreaRecord == null) {
            throw new BizException("城市区域变更记录不存在");
        }

        // 2. 检查行政区域变更记录的状态是否为等待生效状态
        if (!WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue().equals(cityAreaRecord.getChangeStatus())) {
            throw new BizException("只有等待生效状态下才可删除");
        }

        // 3. 执行删除操作 - 删除围栏区域变更记录
        wncFenceAreaChangeRecordsCommandDomainService.delete(input.getFenceAreaChangeRecordsId());

        return true;
    }

}

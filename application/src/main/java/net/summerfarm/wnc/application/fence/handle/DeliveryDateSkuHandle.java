package net.summerfarm.wnc.application.fence.handle;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.DeliveryFenceDTO;
import net.summerfarm.wnc.api.fence.dto.SkuDeliveryDateDTO;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.SkuSubTypEnum;
import net.summerfarm.wnc.common.query.fence.DeliveryFenceDateQuery;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/10/27 14:17<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliveryDateSkuHandle {

    @Resource
    private DeliveryFenceService deliveryFenceService;
    @Resource
    private WncConfig wncConfig;

    /**
     * 查询sku等配送信息
     * @param source 订单来源
     * @param city 城市
     * @param area 区域
     * @param contactId 联系人ID
     * @param merchantId 门店ID
     * @param addOrderFlag 加单标识
     * @param skuList sku集合
     * @param tenantId 租户ID
     * @param poi poi
     * @return 配送信息
     */
    public DeliveryFenceDTO querySkuDelivery(Integer source, String city, String area, Long contactId,
                                             Long merchantId, Boolean addOrderFlag, List<String> skuList,Long tenantId,
                                             String poi) {
        //加单标识
        LocalDateTime payTime = LocalDateTime.now();
        List<Long> chageeTenantIdList = wncConfig.queryChageeTenantIdList();
        if(addOrderFlag != null && addOrderFlag && !chageeTenantIdList.contains(tenantId)){
            payTime = payTime.minusMinutes(30);
        }
        SourceEnum sourceEnum = SourceEnum.orderSourceMap.get(source);
        DeliveryFenceDateQuery deliveryFenceDateQuery = DeliveryFenceDateQuery.builder()
                .orderSourceEnum(sourceEnum == null ? SourceEnum.SAAS_MALL : sourceEnum)
                .contactId(contactId)
                .merchantId(merchantId)
                .orderTime(payTime)
                .skus(skuList)
                .city(city)
                .area(area)
                .tenantId(tenantId)
                .addOrderFlag(addOrderFlag)
                .poi(poi)
                .build();
        DeliveryFenceDTO deliveryFenceDTO = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);

        if (CollectionUtils.isEmpty(deliveryFenceDTO.getDeliveryDateList())) {
            throw new BizException("没有合适的配送日期");
        }
        log.info("xm queryWarehouseStorageFence resp queryDeliveryDateInfo:{}",deliveryFenceDTO);
        if(addOrderFlag){
            List<SkuDeliveryDateDTO> skuDeliveryDates = deliveryFenceDTO.getSkuDeliveryDates();
            if(CollectionUtils.isEmpty(skuDeliveryDates)){
                return deliveryFenceDTO;
            }
            List<SkuDeliveryDateDTO> normalSkuList = skuDeliveryDates.stream().filter(sku -> !Objects.equals(SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode(), sku.getSubType())).collect(Collectors.toList());
            List<SkuDeliveryDateDTO> fullCateSkuList = skuDeliveryDates.stream().filter(sku -> Objects.equals(SkuSubTypEnum.SELF_SALE_NO_WAREHOUSE.getCode(), sku.getSubType())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(fullCateSkuList)){
                return deliveryFenceDTO;
            }
            //存在全品类的单子需要根据当前时间查询配送日期
            deliveryFenceDateQuery.setOrderTime(LocalDateTime.now());
            deliveryFenceDateQuery.setSkus(fullCateSkuList.stream().map(SkuDeliveryDateDTO::getSku).collect(Collectors.toList()));
            DeliveryFenceDTO fullCateFenceDTO = deliveryFenceService.querySkuDeliveryDateInfo(deliveryFenceDateQuery);
            fullCateSkuList = fullCateFenceDTO.getSkuDeliveryDates();

            fullCateSkuList.addAll(normalSkuList);
            deliveryFenceDTO.setSkuDeliveryDates(fullCateSkuList);
        }

        return deliveryFenceDTO;
    }
}

package net.summerfarm.wnc.application.service.changeTask.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskQueryService;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskConverter;
import net.summerfarm.wnc.application.service.changeTask.dto.CustomFenceChangeTaskDetailDTO;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.warehouse.WarehouseLogisticsCenterRepository;
import net.summerfarm.wnc.domain.warehouse.entity.WarehouseLogisticsCenterEntity;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 围栏切仓查询服务类
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangeTaskQueryServiceImpl implements FenceChangeTaskQueryService {

    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private WarehouseLogisticsCenterRepository warehouseLogisticsCenterRepository;

    @Override
    public CustomFenceChangeTaskDetailDTO queryCustomFenceChangeTaskDetail(Long changeTaskId) {
        log.info("查询自定义围栏切仓任务详情，任务ID：{}", changeTaskId);

        // 1. 查询切仓任务基本信息
        FenceChangeTaskEntity fenceChangeTask = fenceChangeTaskRepository.queryById(changeTaskId);
        if (fenceChangeTask == null) {
            throw new BizException("切仓任务不存在");
        }

        // 2. 查询围栏变更记录（包含区域信息）
        List<WncFenceChangeRecordsEntity> fenceChangeRecords = wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeTaskId(changeTaskId);

        if (CollectionUtils.isEmpty(fenceChangeRecords)) {
            log.warn("未找到围栏变更记录，任务ID：{}", changeTaskId);
            return buildEmptyResult(fenceChangeTask);
        }

        // 3. 构建返回结果
        return FenceChangeTaskConverter.converterCustomFenceChangeTaskDetailDTO(fenceChangeTask, fenceChangeRecords);
    }

    @Override
    public LocalTime queryLastCloseTime(Long cityAreaChangeWarehouseRecordId) {
        if (cityAreaChangeWarehouseRecordId == null){
            return null;
        }
        WncCityAreaChangeWarehouseRecordsEntity wncCityAreaChangeWarehouseRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(cityAreaChangeWarehouseRecordId);
        if (wncCityAreaChangeWarehouseRecord == null) {
            return null;
        }

        String changeBatchNo = wncCityAreaChangeWarehouseRecord.getChangeBatchNo();

        WncFenceChangeRecordsQueryParam param = new WncFenceChangeRecordsQueryParam();
        param.setChangeBatchNo(changeBatchNo);
        param.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        List<WncFenceChangeRecordsEntity> wncFenceChangeRecordsEntities = wncFenceChangeRecordsQueryRepository.selectByCondition(param);
        if (CollectionUtils.isEmpty(wncFenceChangeRecordsEntities)) {
            return null;
        }

        List<Integer> storeNoList = wncFenceChangeRecordsEntities.stream().map(WncFenceChangeRecordsEntity::getFenceStoreNo).distinct().collect(Collectors.toList());
        List<WarehouseLogisticsCenterEntity> warehouseLogisticsCenterEntities = warehouseLogisticsCenterRepository.queryListByStoreNos(storeNoList);
        if(CollectionUtils.isEmpty(warehouseLogisticsCenterEntities)){
            return null;
        }

        // 获取最晚的截单时间
        Optional<LocalTime> maxCloseTime = warehouseLogisticsCenterEntities.stream()
                .map(e -> LocalTime.parse(e.getCloseTime()))
                .max(Comparator.naturalOrder());

        return maxCloseTime.get();
    }

    /**
     * 构建空结果
     */
    private CustomFenceChangeTaskDetailDTO buildEmptyResult(FenceChangeTaskEntity fenceChangeTask) {
        CustomFenceChangeTaskDetailDTO result = new CustomFenceChangeTaskDetailDTO();
        result.setChangeAreaName(fenceChangeTask.getChangeAreaName());
        result.setChangeCityName(fenceChangeTask.getChangeCityName());
        if(fenceChangeTask.getType() != null){
            result.setType(fenceChangeTask.getType().getValue());
            result.setTypeDesc(fenceChangeTask.getType().getContent());
        }
        result.setCreateTime(fenceChangeTask.getCreateTime());
        result.setNewFenceAreaList(Collections.emptyList());
        result.setNewAreaGeoShapes(Collections.emptyList());
        result.setOldFenceAreaList(Collections.emptyList());
        result.setOldAreaGeoShapes(Collections.emptyList());
        return result;
    }

}

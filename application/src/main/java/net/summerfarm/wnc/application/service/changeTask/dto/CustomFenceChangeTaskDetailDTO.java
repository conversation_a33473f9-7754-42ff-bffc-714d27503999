package net.summerfarm.wnc.application.service.changeTask.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义围栏切仓任务详情DTO
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CustomFenceChangeTaskDetailDTO {

    /**
     * 变更区域名称
     */
    private String changeAreaName;

    /**
     * 变更城市名称
     */
    private String changeCityName;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 变更前的围栏区域列表
     */
    private List<CustomFenceAreaChangeTaskDTO> oldFenceAreaList;

    /**
     * 变更前的区域地理形状列表
     */
    private List<AreaGeoShapeDTO> oldAreaGeoShapes;

    /**
     * 变更后的围栏区域列表
     */
    private List<CustomFenceAreaChangeTaskDTO> newFenceAreaList;

    /**
     * 变更后的区域地理形状列表
     */
    private List<AreaGeoShapeDTO> newAreaGeoShapes;

    /**
     * 自定义围栏区域变更任务DTO
     */
    @Data
    public static class CustomFenceAreaChangeTaskDTO {
        /**
         * 围栏名称
         */
        private String fenceName;

        /**
         * 围栏状态  0正常  1失效  2删除  3暂停
         */
        private Integer fenceStatus;

        /**
         * 门店名称
         */
        private String storeName;

        /**
         * 区域编号名称
         */
        private String areaNoName;

        /**
         * 自定义区域名称字符串（多个用逗号分隔）
         */
        private String customAreaNameStrs;

        /**
         * 仓库名称（多个用逗号分隔）
         */
        private String warehouseNames;
    }

    /**
     * 区域地理形状DTO
     */
    @Data
    public static class AreaGeoShapeDTO {
        /**
         * 自定义区域名称
         */
        private String customAreaName;

        /**
         * 区域绘制类型
         */
        private Integer areaDrawType;

        /**
         * 地理形状
         */
        private String geoShape;
    }
}

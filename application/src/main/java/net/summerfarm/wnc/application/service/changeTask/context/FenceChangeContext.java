package net.summerfarm.wnc.application.service.changeTask.context;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 围栏变更上下文
 */
@Data
@Builder
public class FenceChangeContext {
    /**
     * 变更批次号
     */
    private String changeBatchNo;

    /**
     * 城市区域变成记录
     */
    private List<WncCityAreaChangeWarehouseRecordsEntity> records;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 自定义围栏的区域
     */
    private String area;

    /**
     * 变更前围栏记录
     */
    private List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords;

    /**
     * 变更后围栏记录
     */
    private List<WncFenceChangeRecordsEntity> afterFenceChangeRecords;

    /**
     * 普通围栏的区域
     */
    private List<String> normalFenceArea;

    public List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsGet() {
        if (CollectionUtils.isEmpty(this.beforeFenceChangeRecords)) {
            return Collections.emptyList();
        }
        
        return this.beforeFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsGet() {
        if (CollectionUtils.isEmpty(this.afterFenceChangeRecords)) {
            return Collections.emptyList();
        }

        return this.afterFenceChangeRecords.stream()
                .map(WncFenceChangeRecordsEntity::getAreaChangeRecords)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }
}
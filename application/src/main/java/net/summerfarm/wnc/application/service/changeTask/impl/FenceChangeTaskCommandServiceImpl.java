package net.summerfarm.wnc.application.service.changeTask.impl;

import net.summerfarm.wnc.application.service.changeTask.FenceChangeTaskCommandService;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceAreaChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncCityAreaChangeWarehouseRecordsCommandDomainService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 切仓操作服务类
 * date: 2025/8/28 16:34<br/>
 *
 * <AUTHOR> />
 */

@Service
public class FenceChangeTaskCommandServiceImpl implements FenceChangeTaskCommandService {

    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsCommandDomainService wncCityAreaChangeWarehouseRecordsCommandDomainService;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;

    /**
     * 围栏区域预约切仓时间
     * @param preExeTime 预约切仓时间
     * @param cityAreaChangeWarehouseRecordId 城市区域变更记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void preExeTime(LocalDateTime preExeTime, Long cityAreaChangeWarehouseRecordId) {
        WncCityAreaChangeWarehouseRecordsEntity cityAreaChangeWarehouseRecord = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(cityAreaChangeWarehouseRecordId);
        // 数据校验
        this.preExeTimeValid(cityAreaChangeWarehouseRecord);

        //获取操作人信息
        UserBase user = UserInfoHolder.getUser();

        // 创建切仓任务
        FenceChangeTaskEntity fenceChangeTaskCreateParm = new FenceChangeTaskEntity();
        if (Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.CUSTOM.getValue(), cityAreaChangeWarehouseRecord.getAreaDefinationType())) {
            fenceChangeTaskCreateParm.setType(FenceChangeTaskEnums.Type.CUSTOM);
        }
        fenceChangeTaskCreateParm.setStatus(FenceChangeTaskEnums.Status.WAIT);
        fenceChangeTaskCreateParm.setExeTime(preExeTime);
        fenceChangeTaskCreateParm.setCreator(user.getNickname());
        fenceChangeTaskCreateParm.setCreatorId(user.getBizUserId());
        fenceChangeTaskCreateParm.setChangeCityName(cityAreaChangeWarehouseRecord.getCity());
        fenceChangeTaskCreateParm.setChangeAreaName(cityAreaChangeWarehouseRecord.getArea());
        Long fenceChangeTaskId = fenceChangeTaskDomainService.createFenceChangeTask(fenceChangeTaskCreateParm);

        // 城市区域预约切仓时间
        wncCityAreaChangeWarehouseRecordsCommandDomainService.preExeTime(cityAreaChangeWarehouseRecordId,preExeTime,fenceChangeTaskId);

    }

    private void preExeTimeValid(WncCityAreaChangeWarehouseRecordsEntity cityAreaChangeWarehouseRecord) {
        if (cityAreaChangeWarehouseRecord == null) {
            throw new BizException("无效城市区域变更记录");
        }
        if (!Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(), cityAreaChangeWarehouseRecord.getChangeStatus())) {
            throw new BizException("非待生效状态，不可预约切仓时间");
        }
        String changeBatchNo = cityAreaChangeWarehouseRecord.getChangeBatchNo();

        // 查询是否有围栏
        WncFenceChangeRecordsQueryParam queryParam = new WncFenceChangeRecordsQueryParam();
        queryParam.setChangeBatchNo(changeBatchNo);
        queryParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
        List<WncFenceChangeRecordsEntity> changeBatchFenceRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(queryParam);
        if (CollectionUtils.isEmpty(changeBatchFenceRecords)) {
            throw new BizException("不存在变更围栏记录，不可预约切仓时间");
        }
    }
}

package net.summerfarm.wnc.application.service.changeTask;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.application.service.changeTask.context.FenceChangeContext;
import net.summerfarm.wnc.application.service.changeTask.converter.FenceChangeTaskOrderConverter;
import net.summerfarm.wnc.application.service.changeTask.factory.FenceChangeTaskContextFactory;
import net.summerfarm.wnc.common.enums.*;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDetailRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskDomainService;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskRepository;
import net.summerfarm.wnc.domain.changeTask.FenceChangeTaskSender;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskOrderEntity;
import net.summerfarm.wnc.domain.fence.AdCodeMsgRepository;
import net.summerfarm.wnc.domain.fence.FenceRepository;
import net.summerfarm.wnc.domain.fence.entity.*;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncCityAreaChangeWarehouseRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.repository.WncFenceChangeRecordsQueryRepository;
import net.summerfarm.wnc.domain.fence.service.WncFenceChangeRecordsQueryDomainService;
import net.summerfarm.wnc.facade.ofc.OfcQueryFacade;
import net.summerfarm.wnc.facade.ofc.dto.FulfillmentOrderDTO;
import net.summerfarm.wnc.facade.ofc.input.FulfillmentQueryInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义区域切订单服务
 * date: 2025/9/5 16:21<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Service
public class FenceChangTaskForCustomFenceOrderChangeHandleService {

    // 错误信息常量
    private static final String ERROR_OLD_FENCE_NOT_MATCHED = "未匹配到旧的围栏";

    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private FenceChangeTaskDomainService fenceChangeTaskDomainService;
    @Resource
    private FenceChangeTaskRepository fenceChangeTaskRepository;
    @Resource
    private FenceChangeTaskDetailRepository fenceChangeTaskDetailRepository;
    @Resource
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Resource
    private FenceChangeTaskContextFactory fenceChangeTaskContextFactory;
    @Resource
    private WncFenceChangeRecordsQueryDomainService wncFenceChangeRecordsQueryDomainService;
    @Resource
    private CustomFenceAreaEsQueryRepository customFenceAreaEsQueryRepository;
    @Resource
    private FenceRepository fenceRepository;
    @Resource
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Resource
    private FenceChangeTaskSender fenceChangeTaskSender;
    @Autowired
    private AdCodeMsgRepository adCodeMsgRepository;

    /**
     * 自定义区域切订单处理
     */
    public void customFenceChangeOrderHandle(FenceChangeTaskEntity waitOrderChangeHandleTask) {
        if (waitOrderChangeHandleTask == null) {
            return;
        }

        // 1. 构建围栏变更上下文
        FenceChangeContext context = buildFenceChangeContext(waitOrderChangeHandleTask);
        if (context == null) {
            return;
        }

        // 2. 判断围栏变更类型并获取变更前的仓库编号
        FenceChangeTypeEnum changeType = wncFenceChangeRecordsQueryDomainService.determineCustomFenceChangeType(context.getBeforeFenceChangeRecords(),context.getAfterFenceChangeRecords());
        List<Integer> beforeStoreNos = getBeforeStoreNos(context, changeType, waitOrderChangeHandleTask);
        if (beforeStoreNos == null) {
            return; // 已在方法内部处理完结任务
        }

        // 3. 查询并保存订单数据
        List<FenceChangeTaskOrderEntity> orderEntities = queryAndSaveOrders(context, beforeStoreNos, waitOrderChangeHandleTask);
        if (CollectionUtils.isEmpty(orderEntities)) {
            completeTask(waitOrderChangeHandleTask);
            return;
        }

        // 4. 处理订单围栏匹配
        processOrderFenceMatching(orderEntities, context, changeType);

        // 5. 执行订单切仓处理
        executeOrderChangeHandling(orderEntities, waitOrderChangeHandleTask);
    }

    /**
     * 构建围栏变更上下文
     */
    private FenceChangeContext buildFenceChangeContext(FenceChangeTaskEntity task) {
        List<WncFenceChangeRecordsEntity> fenceChangeRecordsEntities =
            wncFenceChangeRecordsQueryRepository.selectWithAreaByFenceChangeTaskId(task.getId());

        if (CollectionUtils.isEmpty(fenceChangeRecordsEntities)) {
            log.info("围栏变更记录为空，跳过处理，任务ID:{}", task.getId());
            return null;
        }

        String changeBatchNo = fenceChangeRecordsEntities.get(0).getChangeBatchNo();
        Map<Long, List<WncCityAreaChangeWarehouseRecordsEntity>> taskIdToCityAreaChangMap = wncCityAreaChangeWarehouseRecordsQueryRepository.selectTaskIdMapByFenceChangeTaskIdsChangeStatus(Collections.singletonList(task.getId()),
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue());
        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecordsEntities = taskIdToCityAreaChangMap.getOrDefault(task.getId(), Collections.emptyList());

        FenceChangeContext context = fenceChangeTaskContextFactory.buildFenceChangeContext(changeBatchNo, cityAreaChangeWarehouseRecordsEntities);
        if (context == null) {
            log.info("自定义围栏切仓订单任务ID:{} 无围栏变更上下文，直接完结任务", task.getId());
        }
        return context;
    }

    /**
     * 获取变更前的仓库编号列表
     */
    private List<Integer> getBeforeStoreNos(FenceChangeContext context, FenceChangeTypeEnum changeType, FenceChangeTaskEntity task) {
        List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords = context.getBeforeFenceChangeRecords();
        String city = context.getCity();
        String area = context.getArea();

        switch (changeType) {
            case CUSTOM_TO_CUSTOM:
                return beforeFenceChangeRecords.stream()
                    .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                    .distinct()
                    .collect(Collectors.toList());

            case NONE_TO_CUSTOM:
                // 不涉及切仓直接完结掉即可
                completeTask(task);
                return null;

            case NORMAL_TO_CUSTOM:
                return getStoreNosForNormalToCustom(context, beforeFenceChangeRecords, city, area);

            default:
                log.error("自定义围栏切仓订单处理任务，围栏类型异常，changeBatchNo: {}",
                    context.getBeforeFenceChangeRecords().get(0).getChangeBatchNo());
                return Collections.emptyList();
        }
    }

    /**
     * 处理普通围栏到自定义围栏的仓库编号获取
     */
    private List<Integer> getStoreNosForNormalToCustom(FenceChangeContext context,
                                                       List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords,
                                                       String city, String area) {
        List<WncFenceAreaChangeRecordsEntity> fenceAreaChangeRecords = context.beforeAreaChangeRecordsGet();

        List<WncFenceAreaChangeRecordsEntity> matchedAreaChangeRecords = fenceAreaChangeRecords.stream()
                .filter(e -> Objects.equals(e.getCity(), city) && Objects.equals(e.getArea(), area))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(matchedAreaChangeRecords)) {
            return Collections.emptyList();
        }

        Integer fenceId = matchedAreaChangeRecords.get(0).getFenceId();
        return beforeFenceChangeRecords.stream()
                .filter(e -> Objects.equals(e.getFenceId(), fenceId))
                .map(WncFenceChangeRecordsEntity::getFenceStoreNo)
                .collect(Collectors.toList());
    }

    /**
     * 查询并保存订单数据
     */
    private List<FenceChangeTaskOrderEntity> queryAndSaveOrders(FenceChangeContext context,
                                                                List<Integer> beforeStoreNos,
                                                                FenceChangeTaskEntity task) {
        List<FulfillmentOrderDTO> allFulfillmentOrders = new ArrayList<>();
        String city = context.getCity();
        String area = context.getArea();

        for (Integer storeNo : beforeStoreNos) {
            FulfillmentQueryInput queryInput = FulfillmentQueryInput.builder()
                    .city(city)
                    .areas(Collections.singletonList(area))
                    .storeNo(storeNo)
                    .deliveryDateBegin(task.getExeTimePlus2Date()) // T+2的时间点
                    .build();
            List<FulfillmentOrderDTO> fulfillmentOrders = ofcQueryFacade.queryTimePlus2WaitFulfillmentOrder(queryInput);
            allFulfillmentOrders.addAll(fulfillmentOrders);
        }

        if (CollectionUtils.isEmpty(allFulfillmentOrders)) {
            return Collections.emptyList();
        }

        List<FenceChangeTaskOrderEntity> orderEntities = allFulfillmentOrders.stream()
                .map(FenceChangeTaskOrderConverter::dto2Entity)
                .collect(Collectors.toList());

        orderEntities.forEach(e -> {
            e.setTaskId(task.getId());
            e.setStatus(FenceChangeTaskDetailEnums.Status.WAIT);
        });

        fenceChangeTaskDomainService.saveBatchDetail(orderEntities);
        return orderEntities;
    }

    /**
     * 处理订单围栏匹配
     */
    private void processOrderFenceMatching(List<FenceChangeTaskOrderEntity> orderEntities,
                                          FenceChangeContext context,
                                          FenceChangeTypeEnum changeType) {
        List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList = context.beforeAreaChangeRecordsGet();
        List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList = context.afterAreaChangeRecordsGet();

        orderEntities.forEach(orderInfo -> {
            if (FenceChangeTypeEnum.CUSTOM_TO_CUSTOM.equals(changeType)) {
                processCustomToCustomMatching(orderInfo, beforeAreaChangeRecordsList, afterAreaChangeRecordsList,context.getBeforeFenceChangeRecords());
            } else {
                processNormalToCustomMatching(orderInfo, beforeAreaChangeRecordsList, afterAreaChangeRecordsList);
            }
        });
    }

    /**
     * 处理自定义围栏到自定义围栏的匹配
     */
    private void processCustomToCustomMatching(FenceChangeTaskOrderEntity orderInfo,
                                               List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList,
                                               List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList,
                                               List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        String orderCity = orderInfo.getCity();
        String orderArea = orderInfo.getArea();
        String orderPoi = orderInfo.getPoi();

        // 匹配旧围栏区域
        AdCodeMsgEntity beforeAdCodeMsg = matchAdCodeMsgByPoi(beforeAreaChangeRecordsList, orderCity, orderArea, orderPoi);

        // 匹配新围栏区域
        AdCodeMsgEntity afterAdCodeMsg = matchAdCodeMsgByPoi(afterAreaChangeRecordsList, orderCity, orderArea, orderPoi);

        // 设置围栏区域信息
        customToCustomFenceInstallFenceInfo(orderInfo, beforeAdCodeMsg, afterAdCodeMsg, beforeAreaChangeRecordsList, beforeFenceChangeRecords);
    }

    /**
     * 处理普通围栏到自定义围栏的匹配
     */
    private void processNormalToCustomMatching(FenceChangeTaskOrderEntity orderInfo,
                                              List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList,
                                              List<WncFenceAreaChangeRecordsEntity> afterAreaChangeRecordsList) {
        String orderCity = orderInfo.getCity();
        String orderArea = orderInfo.getArea();
        String orderPoi = orderInfo.getPoi();

        // 匹配订单所属的旧围栏区域
        WncFenceAreaChangeRecordsEntity beforeMatchedAreaChangeRecord = beforeAreaChangeRecordsList.stream()
                .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                .findFirst()
                .orElse(null);

        if (beforeMatchedAreaChangeRecord == null) {
            fenceChangeTaskDomainService.orderChangeFailOrderHandle(orderInfo.getId(), ERROR_OLD_FENCE_NOT_MATCHED);
            return;
        }

        Integer beforeAdCodeMsgId = beforeMatchedAreaChangeRecord.getAdCodeMsgId();
        Integer beforeFenceId = beforeMatchedAreaChangeRecord.getFenceId();

        // 验证旧围栏信息
        FenceEntity beforeFenceEntity = fenceRepository.queryById(beforeFenceId);

        // 匹配新围栏区域（有效配送区域）
        AdCodeMsgEntity matchAfterAdCodeMsg = matchAdCodeMsgByPoi(afterAreaChangeRecordsList, orderCity, orderArea, orderPoi);

        // 设置围栏信息
        setFenceInfoForNormalToCustom(orderInfo, beforeAdCodeMsgId, beforeFenceEntity, matchAfterAdCodeMsg);
    }



    /**
     * 通过POI匹配区域
     */
    private AdCodeMsgEntity matchAdCodeMsgByPoi(List<WncFenceAreaChangeRecordsEntity> areaChangeRecords,
                                                String orderCity, String orderArea, String orderPoi) {
        List<Integer> adCodeMsgIds = areaChangeRecords.stream()
                .filter(e -> Objects.equals(e.getCity(), orderCity) && Objects.equals(e.getArea(), orderArea))
                .filter(e -> Objects.equals(e.getAreaDrawType(), AdCodeMsgEnums.AreaDrawType.DRAWED.getValue()))
                .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(adCodeMsgIds)) {
            return null;
        }

        Integer matchAdCodeMsgId = customFenceAreaEsQueryRepository.matchEsByAdCodeMsgIdsWithPoi(adCodeMsgIds, orderPoi);
        if(matchAdCodeMsgId == null){
            // 默认取未绘制区域
            matchAdCodeMsgId = areaChangeRecords.stream()
                    .filter(e -> Objects.equals(e.getAdCodeMsgDetailEntity().getAreaDrawType(), AdCodeMsgEnums.AreaDrawType.UNDRAW.getValue()))
                    .map(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId)
                    .findFirst()
                    .orElse(null);
        }
        if (matchAdCodeMsgId == null) {
            return null;
        }

        return adCodeMsgRepository.queryById(matchAdCodeMsgId);
    }

    /**
     * 设置围栏信息（自定义到自定义）
     */
    private void customToCustomFenceInstallFenceInfo(FenceChangeTaskOrderEntity orderInfo,
                                                     AdCodeMsgEntity beforeAdCodeMsg,
                                                     AdCodeMsgEntity afterAdCodeMsg,
                                                     List<WncFenceAreaChangeRecordsEntity> beforeAreaChangeRecordsList,
                                                     List<WncFenceChangeRecordsEntity> beforeFenceChangeRecords) {
        if (beforeAdCodeMsg == null) {
            beforeAdCodeMsg = new AdCodeMsgEntity();
        }
        if (afterAdCodeMsg == null) {
            afterAdCodeMsg = new AdCodeMsgEntity();
        }

        Map<Integer, Long> adCodeMsgIdToFenceChangeIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(beforeAreaChangeRecordsList)) {
            adCodeMsgIdToFenceChangeIdMap = beforeAreaChangeRecordsList.stream()
                    .collect(Collectors.toMap(WncFenceAreaChangeRecordsEntity::getAdCodeMsgId, WncFenceAreaChangeRecordsEntity::getFenceChangeId, (o, n) -> n));
        }

        Map<Long, FenceEntity> fenceChangeIdToFenceEntityMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(beforeFenceChangeRecords)) {
            fenceChangeIdToFenceEntityMap = beforeFenceChangeRecords.stream()
                    .collect(Collectors.toMap(WncFenceChangeRecordsEntity::getId, WncFenceChangeRecordsEntity::getFenceDetailEntity, (o, n) -> n));
        }

        Integer beforeAdCodeMsgId = beforeAdCodeMsg.getId();

        Long beforeFenceChangeId = adCodeMsgIdToFenceChangeIdMap.get(beforeAdCodeMsgId);
        FenceEntity beforeFenceEntity = fenceChangeIdToFenceEntityMap.get(beforeFenceChangeId);

        Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(afterAdCodeMsg.getId()));
        FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(afterAdCodeMsg.getId());

        orderInfo.setOldAdCodeMsgId(beforeAdCodeMsg.getId());
        if (beforeFenceEntity != null) {
            orderInfo.setOldFenceId(beforeFenceEntity.getId());
            orderInfo.setOldStoreNo(beforeFenceEntity.getStoreNo());
            orderInfo.setOldAreaNo(beforeFenceEntity.getAreaNo());
        }

        orderInfo.setNewAdCodeMsgId(afterAdCodeMsg.getId());
        if (afterFenceEntity != null) {
            orderInfo.setNewFenceId(afterFenceEntity.getId());
            orderInfo.setNewStoreNo(afterFenceEntity.getStoreNo());
            orderInfo.setNewAreaNo(afterFenceEntity.getAreaNo());
            orderInfo.setNewFenceName(afterFenceEntity.getFenceName());
            orderInfo.setNewCustomAreaName(afterAdCodeMsg.getCustomAreaName());
        } else {
            orderInfo.setStatus(FenceChangeTaskDetailEnums.Status.NO_NEED);
            orderInfo.setRemark("不在新围栏有效范围内");
        }

        if(!Objects.equals(afterAdCodeMsg.getStatus(),AdCodeMsgEnums.Status.VALID.getValue())){
            orderInfo.setStatus(FenceChangeTaskDetailEnums.Status.NO_NEED);
            orderInfo.setRemark("不在新围栏有效范围内");
        }

        if (Objects.equals(orderInfo.getOldStoreNo(), orderInfo.getNewStoreNo()) && Objects.equals(orderInfo.getOldAreaNo(), orderInfo.getNewAreaNo())
                && Objects.equals(orderInfo.getOldFenceId(), orderInfo.getNewFenceId()) && Objects.equals(orderInfo.getOldAdCodeMsgId(), orderInfo.getNewAdCodeMsgId())) {
            orderInfo.setRemark("无需处理");
            orderInfo.setStatus(FenceChangeTaskDetailEnums.Status.NO_NEED);
            fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
        } else {
            fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
        }

    }

    /**
     * 设置围栏信息（普通到自定义）
     */
    private void setFenceInfoForNormalToCustom(FenceChangeTaskOrderEntity orderInfo,
                                              Integer beforeAdCodeMsgId,
                                              FenceEntity beforeFenceEntity,
                                               AdCodeMsgEntity matchAfterAdCodeMsg) {

        orderInfo.setOldAdCodeMsgId(beforeAdCodeMsgId);
        if(beforeFenceEntity != null){
            orderInfo.setOldFenceId(beforeFenceEntity.getId());
            orderInfo.setOldStoreNo(beforeFenceEntity.getStoreNo());
            orderInfo.setOldAreaNo(beforeFenceEntity.getAreaNo());
        }

        if(matchAfterAdCodeMsg != null){
            Map<Integer, FenceEntity> afterAdMsgIdTOFenceEntityMap = fenceRepository.queryByAdCodeMsgIds(Collections.singletonList(matchAfterAdCodeMsg.getId()));
            FenceEntity afterFenceEntity = afterAdMsgIdTOFenceEntityMap.get(matchAfterAdCodeMsg.getId());
            orderInfo.setNewAdCodeMsgId(matchAfterAdCodeMsg.getId());
            if (afterFenceEntity != null) {
                orderInfo.setNewFenceId(afterFenceEntity.getId());
                orderInfo.setNewStoreNo(afterFenceEntity.getStoreNo());
                orderInfo.setNewAreaNo(afterFenceEntity.getAreaNo());
                orderInfo.setNewFenceName(afterFenceEntity.getFenceName());
                orderInfo.setNewCustomAreaName(matchAfterAdCodeMsg.getCustomAreaName());
            } else {
                orderInfo.setStatus(FenceChangeTaskDetailEnums.Status.NO_NEED);
                orderInfo.setRemark("不在新围栏有效范围内");
            }

            // 非有效区域
            if(Objects.equals(AdCodeMsgEnums.Status.VALID.getValue(),matchAfterAdCodeMsg.getStatus())){
                orderInfo.setStatus(FenceChangeTaskDetailEnums.Status.NO_NEED);
                orderInfo.setRemark("不在新围栏有效范围内");
            }
        }

        fenceChangeTaskDomainService.updateOldNewFenceInfo(orderInfo);
    }

    /**
     * 执行订单切仓处理
     */
    private void executeOrderChangeHandling(List<FenceChangeTaskOrderEntity> orderEntities, FenceChangeTaskEntity task) {
        List<String> failOrders = new ArrayList<>();

        // 执行切仓
        orderEntities.forEach(orderInfo -> {
            try {
                // 过滤无需切仓的订单数据
                if(!Objects.equals(orderInfo.getStatus(),FenceChangeTaskDetailEnums.Status.WAIT)){
                    return;
                }
                fenceChangeTaskDomainService.doFenceChangeOrderHandleNew(orderInfo);
            } catch (Exception e) {
                failOrders.add(orderInfo.getOuterOrderId());
                log.info("围栏切仓订单处理任务-履约单处理失败，等待失败订单重试，异常原因：{}", e.getMessage(), e);
                FenceChangeTaskOrderEntity updateOrderFail = orderInfo.execute(FenceChangeTaskDetailEnums.Status.FAIL, e.getMessage());
                fenceChangeTaskDetailRepository.update(updateOrderFail);
            }
        });

        // 更新切仓任务状态
        completeTask(task);

        // 发送失败通知
        if (!failOrders.isEmpty()) {
            fenceChangeTaskSender.sendOrderFailMsg(task, failOrders.size());
        }
    }

    /**
     * 完成任务
     */
    private void completeTask(FenceChangeTaskEntity task) {
        fenceChangeTaskRepository.update(task.execute(FenceChangeTaskEnums.Status.COMPLETED));
    }
}

package net.summerfarm.wnc.api.fence.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:围栏区域数据转换对象
 * date: 2023/8/25 17:19
 *
 * <AUTHOR>
 */
@Data
public class FenceAreaDTO implements Serializable {

    private static final long serialVersionUID = 4557359435404883361L;

    /**
     * 行政区ID
     */
    private Integer id;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 等级
     */
    private String level;

    /**
     * 发起预约切仓任务可选状态，true：是，false:否
     */
    private Boolean selectFlag;

    /**
     * 自定义区域名称
     */
    private String customAreaName;
}

package net.summerfarm.wnc.api.fence.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Description:联系人地址请求参数
 * date: 2024/1/3 15:33
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressQueryInput implements Serializable {

    private static final long serialVersionUID = -2616139646581219060L;

    /**
     * 省
     */
    @NotBlank(message = "省不能为空")
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;
}

package net.summerfarm.wnc.inbound.provider.fence;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.api.fence.dto.AddressBelongFenceDTO;
import net.summerfarm.wnc.api.fence.dto.DeliveryFenceCloseTimeQuery;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.api.fence.dto.WarehouseInventoryMappingDTO;
import net.summerfarm.wnc.api.fence.dto.area.AreaDTO;
import net.summerfarm.wnc.api.fence.dto.area.SimpleAreaDTO;
import net.summerfarm.wnc.api.fence.input.CityAreaPoiBelongFenceQueryInput;
import net.summerfarm.wnc.api.fence.service.DeliveryFenceService;
import net.summerfarm.wnc.api.fence.service.FenceService;
import net.summerfarm.wnc.api.fence.service.WarehouseInventoryMappingService;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.*;
import net.summerfarm.wnc.client.req.fence.*;
import net.summerfarm.wnc.client.resp.*;
import net.summerfarm.wnc.client.resp.fence.*;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.constants.AppConsts;
import net.summerfarm.wnc.common.query.fence.AddressQuery;
import net.summerfarm.wnc.common.query.fence.AreaByListWarehouseAndSkuQuery;
import net.summerfarm.wnc.common.query.fence.FenceQuery;
import net.summerfarm.wnc.common.query.warehouse.StoreNoQuery;
import net.summerfarm.wnc.inbound.converter.DubboAreaQueryConverter;
import net.summerfarm.wnc.inbound.provider.fence.converter.ContactAddressReqConverter;
import net.summerfarm.wnc.inbound.provider.fence.converter.DeliveryFenceQueryConverter;
import net.summerfarm.wnc.inbound.provider.fence.converter.FenceRespConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.security.ProviderException;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023-07-21
 **/
@Slf4j
@DubboService
public class DeliveryFenceQueryProviderImpl implements DeliveryFenceQueryProvider {

	@Autowired
	private DeliveryFenceQueryConverter deliveryFenceQueryConverter;
	@Resource
	private DeliveryFenceService deliveryFenceService;
	@Resource
	private DubboAreaQueryConverter dubboAreaQueryConverter;
	@Resource
	private WarehouseInventoryMappingService warehouseInventoryMappingService;
	@Resource
	private FenceService fenceService;
	@Resource
	private WncConfig wncConfig;

	@Override
	public DubboResponse<DeliveryFenceResp> queryDeliveryFence(@Valid DeliveryFenceQueryReq deliveryFenceQueryReq) {
		String city = deliveryFenceQueryReq.getCity();
		String area = deliveryFenceQueryReq.getArea();
		String poi = deliveryFenceQueryReq.getPoi();
		FenceDTO fenceDTO = deliveryFenceService.queryDeliveryFenceByCityAreaPoi(city, area, poi);
		return DubboResponse.getOK(FenceRespConverter.dto2Resp(fenceDTO));
	}

	@Override
	public DubboResponse<FenceCloseTimeResp> queryCloseTime(@Valid FenceCloseTimeQueryReq fenceCloseTimeQueryReq) {
		if(wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(fenceCloseTimeQueryReq.getPoi())){
			log.error("\n poi参数为空 DeliveryFenceQueryProvider queryCloseTime poi is null\n");
		}
		DeliveryFenceCloseTimeQuery query = deliveryFenceQueryConverter.dubboDTOToQueryDTO(fenceCloseTimeQueryReq);
		FenceCloseTimeResp fenceCloseTimeResp = new FenceCloseTimeResp();
		fenceCloseTimeResp.setCloseTime(deliveryFenceService.queryCloseTime(query));
		return DubboResponse.getOK(fenceCloseTimeResp);
	}

	@Override
	public DubboResponse<List<QueryAreaLegitimacyResp>> queryAreaLegitimacyResp(@Valid QueryAreaLegitimacyReq queryAreaLegitimacyReq) {
		List<SimpleAreaDTO> simpleAreaDTOS = deliveryFenceService.queryAreaLegitimacy(queryAreaLegitimacyReq.getCityList());
		return DubboResponse.getOK(dubboAreaQueryConverter.simpleDtoToResp(simpleAreaDTOS));
	}

	@Override
	public DubboResponse<AreaQueryResp> queryAreaByAddress(@Valid AreaQueryReq areaQueryReq) {
		if(wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(areaQueryReq.getPoi())){
			log.error("\n poi参数为空 DeliveryFenceQueryProvider queryAreaByAddress poi is null\n");
		}
		AreaDTO areaDTO = deliveryFenceService.queryAreaByAddressWithPoi(
				areaQueryReq.getCity(), areaQueryReq.getArea(),areaQueryReq.getPoi());

		return DubboResponse.getOK(dubboAreaQueryConverter.dtoToDubboResp(areaDTO));
	}

	@Override
	public DubboResponse<StoreQueryResp> queryStoreByAddress(@Valid StoreQueryReq storeQueryReq) {
		if (wncConfig.isCustomFencePoiWarningSwitchOpen() && StringUtils.isEmpty(storeQueryReq.getPoi())) {
			log.error("\n poi参数为空 DeliveryFenceQueryProvider queryStoreByAddress poi is null\n");
		}
		StoreNoQuery storeNoQuery = StoreNoQuery.builder()
				.city(storeQueryReq.getCity())
				.area(storeQueryReq.getArea())
				.poi(storeQueryReq.getPoi())
				.contactId(storeQueryReq.getContactId())
				.tenantId(storeQueryReq.getTenantId()).build();
		FenceDTO fenceDTO = deliveryFenceService.queryStoreNo(storeNoQuery);

		StoreQueryResp resp = new StoreQueryResp();
		if(fenceDTO != null){
			resp.setStoreNo(fenceDTO.getStoreNo());
			resp.setIsStoreNoAppointment(fenceDTO.getIsStoreNoAppointment());
			resp.setStoreName(fenceDTO.getStoreName());
			resp.setFulfillmentType(fenceDTO.getFulfillmentMethod());
		}
		return DubboResponse.getOK(resp);
	}

	@Override
	public DubboResponse<List<DeliveryFenceResp>> batchQueryDeliveryFenceByCity(@Valid DeliveryFenceByCityQueryReq deliveryFenceByCityQueryReq) {
		List<FenceDTO> fenceDTOList = deliveryFenceService.batchQueryDeliveryFenceByCity(deliveryFenceByCityQueryReq.getCityNames());
		return DubboResponse.getOK(fenceDTOList.stream().map(FenceRespConverter::dto2Resp).collect(Collectors.toList()));
	}

	@Override
	public DubboResponse<List<ContactAddressBelongFenceResp>> batchQueryContactAddressBelongFence(@Valid ContactAddressBatchQueryReq contactAddressBatchQueryReq) {
		List<ContactAddressQueryReq> addressQueryReqList = contactAddressBatchQueryReq.getContactAddressQueryReqList();
		if (wncConfig.isCustomFencePoiWarningSwitchOpen()) {
			List<ContactAddressQueryReq> isBlankPoiList = addressQueryReqList.stream()
					.filter(e -> StringUtils.isEmpty(e.getPoi()))
					.collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(isBlankPoiList)) {
				log.error("\n poi参数为空 DeliveryFenceQueryProvider batchQueryContactAddressBelongFence poi is null\n");
			}
		}

		List<CityAreaPoiBelongFenceQueryInput> queryInputs = addressQueryReqList.stream().map(reqInput -> {
			CityAreaPoiBelongFenceQueryInput input = new CityAreaPoiBelongFenceQueryInput();
			AddressQueryReq addressReq = reqInput.getAddressReq();
			if(reqInput.getAddressReq() != null){
				input.setArea(addressReq.getArea());
				input.setCity(addressReq.getCity());
			}
			input.setPoi(reqInput.getPoi());
			input.setBusinessId(String.valueOf(reqInput.getContactId()));
			return input;
		}).collect(Collectors.toList());

		List<AddressBelongFenceDTO> addressBelongFenceDTOList = deliveryFenceService.cityAreaPoiBelongFenceQueryList(queryInputs);

		List<ContactAddressBelongFenceResp> respList = addressBelongFenceDTOList.stream().map(e -> {
			ContactAddressBelongFenceResp resp = new ContactAddressBelongFenceResp();
			resp.setArea(e.getArea());
			resp.setCity(e.getCity());
			resp.setContactId(Long.parseLong(e.getBusinessId()));
			resp.setDeliveryFenceResp(FenceRespConverter.dto2Resp(e.getFenceDTO()));
			return resp;
		}).collect(Collectors.toList());

		return DubboResponse.getOK(respList);
	}

	@Override
	public DubboResponse<List<CityAreaBelongFenceResp>> batchQueryCityAreaBelongFence(@Valid CityAreaBatchQueryReq cityAreaBatchQueryReq) {
		if (wncConfig.isCustomFencePoiWarningSwitchOpen()) {
			log.error("\n 接口已废弃，无需在调用 DeliveryFenceQueryProvider batchQueryCityAreaBelongFence\n");
		}
		List<AddressQuery> addressQueryList = cityAreaBatchQueryReq.getAreaQueryReqList().stream().map(ContactAddressReqConverter::areaReq2query).collect(Collectors.toList());
		List<AddressBelongFenceDTO> result = deliveryFenceService.batchQueryAddressBelongFence(addressQueryList);
		//去重相同 城市#区域数据
		Map<String/*city#area*/, AddressBelongFenceDTO> cityAreaFenceMap = Optional.ofNullable(result).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(e -> e.getCity() + AppConsts.Symbol.HASH_TAG + e.getArea(), Function.identity(), (oldData, newData) -> newData));

		List<CityAreaBelongFenceResp> cityAreaBelongFenceRespList = cityAreaFenceMap.values().stream().map(FenceRespConverter::cityAreaBelongFenceDto2Resp).collect(Collectors.toList());
		return DubboResponse.getOK(cityAreaBelongFenceRespList);
	}

	@Override
	public DubboResponse<List<FenceResp>> queryFenceListWithArea(FenceQueryReq fenceQueryReq) {
		if (fenceQueryReq.getFenceId() == null
				&&fenceQueryReq.getAreaNo() == null
				&& fenceQueryReq.getStoreNo() == null
				&& fenceQueryReq.getStatus() == null
				&& CollectionUtils.isEmpty(fenceQueryReq.getFenceIds())
				&& CollectionUtils.isEmpty(fenceQueryReq.getAreaNos())
				&& CollectionUtils.isEmpty(fenceQueryReq.getStoreNos())) {
			throw new ProviderException("缺少必要查询参数");
		}
		FenceQuery fenceQuery = FenceQuery.builder()
				.fenceId(fenceQueryReq.getFenceId())
                .areaNo(fenceQueryReq.getAreaNo())
                .storeNo(fenceQueryReq.getStoreNo())
                .fenceIds(fenceQueryReq.getFenceIds())
                .areaNos(fenceQueryReq.getAreaNos())
                .storeNos(fenceQueryReq.getStoreNos())
				.status(fenceQueryReq.getStatus()).build();
        List<FenceDTO> fenceDTOList = deliveryFenceService.queryFenceListWithArea(fenceQuery);
		List<FenceResp> fenceRespList = Optional.ofNullable(fenceDTOList).orElse(Lists.newArrayList()).stream().map(FenceRespConverter::fenceDto2Resp).collect(Collectors.toList());
		return DubboResponse.getOK(fenceRespList);
	}

	@Override
	public DubboResponse<List<AreaResp>> queryAreaByWarehouseAndSku(@Valid SkuWarehouseNoQueryAreaReq req) {
		//根据仓库编号和sku编号查询城配仓编号
		List<Integer> storeNos = warehouseInventoryMappingService.queryStoreNoByWarehouseAndSku(req.getWarehouseNo(), req.getSku());
		if (CollectionUtils.isEmpty(storeNos)) {
			return DubboResponse.getOK(Lists.newArrayList());
		}
		//根据城配仓编号查询围栏信息
		List<Integer> areaNos = fenceService.queryValidAreaListByStoreNos(storeNos);

		List<AreaResp> areaResps = areaNos.stream().map(areaNo -> {
			AreaResp areaResp = new AreaResp();
			areaResp.setAreaNo(areaNo);
			return areaResp;
		}).collect(Collectors.toList());

		return DubboResponse.getOK(areaResps);
	}

	@Override
	public DubboResponse<List<AreaWarehouseNoSkuResp>> queryAreaByListWarehouseAndSku(@Valid AreaQueryWarehouseNoSkuReq req) {
		List<WarehouseInventoryMappingDTO> warehouseInventoryMappingDTOS = warehouseInventoryMappingService.queryAreaByListWarehouseAndSku(
				req.getSkuWarehouseNoQueryAreaReqList().stream()
				.map(warehouseNoAreaReq -> AreaByListWarehouseAndSkuQuery.builder()
						.sku(warehouseNoAreaReq.getSku())
						.warehouseNo(warehouseNoAreaReq.getWarehouseNo())
						.build())
				.collect(Collectors.toList()));

		return DubboResponse.getOK(warehouseInventoryMappingDTOS.stream().map(dto -> {
			return AreaWarehouseNoSkuResp.builder()
					.areaNos(dto.getAreaNos())
					.sku(dto.getSku())
					.warehouseNo(dto.getWarehouseNo())
					.build();
		}).collect(Collectors.toList()));
	}

	@Override
	public DubboResponse<CityAreaBelongFencesResp> batchQueryCityAreaBelongFenceList(@Valid CityAreaBelongFenceBatchQueryReq req) {
		List<AddressQuery> addressQueryList = req.getCityAreaQueryReqList().stream()
				.map(ContactAddressReqConverter::cityAreaQueryReq2Query)
				.collect(Collectors.toList());

		List<FenceDTO> fenceDTOS = deliveryFenceService.batchQueryCityAreaBelongFenceList(addressQueryList);

		CityAreaBelongFencesResp resultResp = new CityAreaBelongFencesResp();
		resultResp.setFenceList(fenceDTOS.stream().map(FenceRespConverter::fenceDto2Resp).collect(Collectors.toList()));

		return DubboResponse.getOK(resultResp);
	}

	@Override
	public DubboResponse<CityAreaPoiBelongFenceResp> cityAreaPoiBelongFenceQueryList(@Valid CityAreaPoiBelongFenceQueryReq req) {
		List<CityAreaPoiBelongFenceQueryReq.CityAreaPoi> cityAreaPoiList = req.getCityAreaPois();

		List<CityAreaPoiBelongFenceQueryInput> queryInputs = cityAreaPoiList.stream().map(reqInput -> {
			CityAreaPoiBelongFenceQueryInput input = new CityAreaPoiBelongFenceQueryInput();
			input.setPoi(reqInput.getPoi());
			input.setArea(reqInput.getArea());
			input.setCity(reqInput.getCity());
			return input;
		}).collect(Collectors.toList());

		List<AddressBelongFenceDTO> addressBelongFenceDTOList = deliveryFenceService.cityAreaPoiBelongFenceQueryList(queryInputs);
		if (CollectionUtils.isEmpty(addressBelongFenceDTOList)) {
			return DubboResponse.getOK(new CityAreaPoiBelongFenceResp());
		}

		List<CityAreaPoiBelongFenceResp.CityAreaPoiBelongFence> cityAreaPoiBelongFenceList = addressBelongFenceDTOList.stream().map(e -> {
			CityAreaPoiBelongFenceResp.CityAreaPoiBelongFence deliveryResp = new CityAreaPoiBelongFenceResp.CityAreaPoiBelongFence();
			deliveryResp.setArea(e.getArea());
			deliveryResp.setCity(e.getCity());
			deliveryResp.setPoi(e.getPoi());
			if (e.getFenceDTO() != null) {
				deliveryResp.setFenceId(e.getFenceDTO().getFenceId());
				deliveryResp.setFenceName(e.getFenceDTO().getFenceName());
				deliveryResp.setStatus(e.getFenceDTO().getStatus());
				deliveryResp.setStoreNo(e.getFenceDTO().getStoreNo());
			}
			return deliveryResp;
		}).collect(Collectors.toList());


		CityAreaPoiBelongFenceResp resp = new CityAreaPoiBelongFenceResp();
		resp.setCityAreaPoiBelongFenceList(cityAreaPoiBelongFenceList);

		return DubboResponse.getOK(resp);
	}
}

package net.summerfarm.wnc.inbound.provider.config.converter;

import lombok.Data;
import net.summerfarm.wnc.api.fence.input.AddressQueryInput;
import net.summerfarm.wnc.api.config.input.ContactConfigUpdateCommandInput;
import net.summerfarm.wnc.client.req.AddressQueryReq;
import net.summerfarm.wnc.client.req.storeConfig.ContactConfigAdjustCommandReq;

/**
 * Description:联系人配置command转化器
 * date: 2024/1/3 18:07
 *
 * <AUTHOR>
 */
@Data
public class ContactConfigCommandConverter {

    public static ContactConfigUpdateCommandInput req2Command(ContactConfigAdjustCommandReq contactConfigAdjustCommandReq){
        ContactConfigUpdateCommandInput contactConfigUpdateCommandInput = new ContactConfigUpdateCommandInput();
        contactConfigUpdateCommandInput.setTenantId(contactConfigAdjustCommandReq.getTenantId());
        contactConfigUpdateCommandInput.setOuterContactId(contactConfigAdjustCommandReq.getContactId());
        contactConfigUpdateCommandInput.setFulfillmentMethod(contactConfigAdjustCommandReq.getFulfillmentMethod());

        AddressQueryReq addressReq = contactConfigAdjustCommandReq.getAddressReq();
        AddressQueryInput addressQueryInput = AddressQueryInput.builder()
                .province(addressReq.getProvince())
                .city(addressReq.getCity())
                .area(addressReq.getArea())
                .poi(contactConfigAdjustCommandReq.getPoi())
                .address(addressReq.getAddress()).build();
        contactConfigUpdateCommandInput.setAddressQuery(addressQueryInput);
        contactConfigUpdateCommandInput.setStoreNo(contactConfigAdjustCommandReq.getStoreNo());
        return contactConfigUpdateCommandInput;
    }
}

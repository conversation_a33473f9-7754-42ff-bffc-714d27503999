package net.summerfarm.wnc.inbound.provider.fence.converter;

import net.summerfarm.wnc.api.fence.dto.AddressBelongFenceDTO;
import com.google.common.collect.Lists;
import net.summerfarm.wnc.api.fence.dto.FenceAreaDTO;
import net.summerfarm.wnc.api.fence.dto.FenceDTO;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.fence.CityAreaBelongFenceResp;
import net.summerfarm.wnc.client.resp.fence.FenceAreaResp;
import net.summerfarm.wnc.client.resp.fence.FenceResp;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:围栏Resp转换器
 * date: 2023/6/8 11:42
 *
 * <AUTHOR>
 */
public class FenceRespConverter {

    public static DeliveryFenceResp dto2Resp(FenceDTO fenceDTO){
        if(fenceDTO == null){
            return null;
        }
        DeliveryFenceResp deliveryFenceResp = new DeliveryFenceResp();
        deliveryFenceResp.setFenceId(fenceDTO.getFenceId());
        deliveryFenceResp.setFenceName(fenceDTO.getFenceName());
        deliveryFenceResp.setStoreNo(fenceDTO.getStoreNo());
        deliveryFenceResp.setAreaNo(fenceDTO.getAreaNo());
        deliveryFenceResp.setCityName(fenceDTO.getCityName());
        deliveryFenceResp.setStatus(fenceDTO.getStatus());
        deliveryFenceResp.setOrderChannelType(fenceDTO.getOrderChannelType());
        if (!CollectionUtils.isEmpty(fenceDTO.getFenceAreaDTOList())) {
            FenceAreaDTO areaDTO = fenceDTO.getFenceAreaDTOList().get(0);
            deliveryFenceResp.setAdCodeMsgId(areaDTO.getId());
            deliveryFenceResp.setCustomAreaName(areaDTO.getCustomAreaName());
        }
        return deliveryFenceResp;
    }

    public static CityAreaBelongFenceResp cityAreaBelongFenceDto2Resp(AddressBelongFenceDTO addressBelongFenceDTO){
        if(addressBelongFenceDTO == null){
            return null;
        }
        CityAreaBelongFenceResp cityAreaBelongFenceResp = new CityAreaBelongFenceResp();
        cityAreaBelongFenceResp.setCity(addressBelongFenceDTO.getCity());
        cityAreaBelongFenceResp.setArea(addressBelongFenceDTO.getArea());
        FenceDTO fenceDTO = addressBelongFenceDTO.getFenceDTO();
        cityAreaBelongFenceResp.setFenceResp(fenceDto2Resp(fenceDTO));
        return cityAreaBelongFenceResp;
    }

    public static FenceResp fenceDto2Resp(FenceDTO fenceDTO){
        if(fenceDTO == null){
            return null;
        }
        FenceResp fenceResp = new FenceResp();
        fenceResp.setFenceId(fenceDTO.getFenceId());
        fenceResp.setFenceName(fenceDTO.getFenceName());
        fenceResp.setStoreNo(fenceDTO.getStoreNo());
        fenceResp.setAreaNo(fenceDTO.getAreaNo());
        fenceResp.setStatus(fenceDTO.getStatus());
        List<FenceAreaDTO> fenceAreaDTOList = fenceDTO.getFenceAreaDTOList();
        List<FenceAreaResp> areaRespList = Optional.ofNullable(fenceAreaDTOList).orElse(Lists.newArrayList()).stream().map(FenceAreaRespConverter::dto2Resp).collect(Collectors.toList());
        fenceResp.setFenceAreaRespList(areaRespList);
        return fenceResp;
    }
}

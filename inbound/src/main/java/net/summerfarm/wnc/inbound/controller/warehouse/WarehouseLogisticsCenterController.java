package net.summerfarm.wnc.inbound.controller.warehouse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.api.warehouse.dto.CustomFenceSameCityAreaWarehouseLogisticsMappingDTO;
import net.summerfarm.wnc.api.warehouse.input.CustomFenceSameCityAreaMappingQueryInput;
import net.summerfarm.wnc.api.warehouse.input.LogisticsMappingDeleteInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsSaveInput;
import net.summerfarm.wnc.api.warehouse.input.WarehouseLogisticsUpdateInput;
import net.summerfarm.wnc.api.warehouse.service.warehouse.WarehouseLogisticsService;
import net.summerfarm.wnc.common.enums.WarehouseLogisticsCenterEnums;
import net.summerfarm.wnc.common.query.warehouse.WarehouseLogisticsQuery;
import net.summerfarm.wnc.inbound.controller.warehouse.converter.WarehouseLogisticsCenterVOConverter;
import net.summerfarm.wnc.inbound.controller.warehouse.vo.*;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *城配仓
 * <AUTHOR> />
 */
@RestController
@RequestMapping("/warehouse-logistics")
public class WarehouseLogisticsCenterController {

    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;

    /**
     * 获取城配仓分页列表
     */
    @PostMapping("/query/logistics")
    @RequiresPermissions(value = {"warehouse-logistics:select"}, logical = Logical.OR)
    public CommonResult<PageInfo<WarehouseLogisticsListVO>> queryLogisticsList(@RequestBody WarehouseLogisticsQuery warehouseLogisticsQuery) {
        return CommonResult.ok(WarehouseLogisticsCenterVOConverter.dtoPage2VOPage(warehouseLogisticsService.queryLogisticsList(warehouseLogisticsQuery)));
    }

    /**
     * 获取城配仓详情
     */
    @PostMapping("/query/logistics-detail")
    @RequiresPermissions(value = {"warehouse-logistics:select"}, logical = Logical.OR)
    public CommonResult<WarehouseLogisticsDetailVO> queryLogisticsDetail(@RequestParam Integer id) {
        return CommonResult.ok(WarehouseLogisticsCenterVOConverter.detailDTO2VO(warehouseLogisticsService.queryLogisticsDetail(id)));
    }

    /**
     * 修改城配仓信息
     */
    @PostMapping("/upsert/logistics-update")
    @RequiresPermissions(value = {"warehouse-logistics:update"}, logical = Logical.OR)
    public CommonResult<Void> updateLogistics(@RequestBody @Validated WarehouseLogisticsUpdateInput warehouseLogisticsUpdateCommand) {
        warehouseLogisticsService.updateLogistics(warehouseLogisticsUpdateCommand);
        return CommonResult.ok();
    }

    /**
     * 根据城配仓查询相同城市区域自定义围栏使用关系信息
     * @param input 参数
     * @return 结果
     */
    @PostMapping("/query/custom-fence-same-city-area-warehouse-logistics-mapping")
    public CommonResult<List<CustomFenceSameCityAreaWarehouseLogisticsMappingVO>> queryCustomFenceSameCityAreaWarehouseLogisticsMapping(@RequestBody @Validated CustomFenceSameCityAreaMappingQueryInput input) {
        List<CustomFenceSameCityAreaWarehouseLogisticsMappingDTO> dtoList = warehouseLogisticsService.queryCustomFenceSameCityAreaWarehouseLogisticsMapping(input);

        List<CustomFenceSameCityAreaWarehouseLogisticsMappingVO> voList = dtoList.stream().map(e -> {
            CustomFenceSameCityAreaWarehouseLogisticsMappingVO vo = new CustomFenceSameCityAreaWarehouseLogisticsMappingVO();

            vo.setFenceId(e.getFenceId());
            vo.setFenceName(e.getFenceName());
            vo.setFenceType(e.getFenceType());
            vo.setStoreName(e.getStoreName());
            vo.setStoreNo(e.getStoreNo());
            vo.setWarehouseNameList(e.getWarehouseNameList());
            vo.setWarehouseNoList(e.getWarehouseNoList());

            return vo;
        }).collect(Collectors.toList());

        return CommonResult.ok(voList);
    }

    /**
     * 删除配送中心使用库存仓
     */
    @PostMapping("/upsert/mapping-delete")
    @RequiresPermissions(value = {"warehouse-logistics:delete"}, logical = Logical.OR)
    public CommonResult<Void> mappingDelete(@RequestBody @Validated LogisticsMappingDeleteInput input) {
        warehouseLogisticsService.mappingDelete(input);
        return CommonResult.ok();
    }

    /**
     * 新增城配仓信息
     */
    @PostMapping("/upsert/logistics-save")
    @RequiresPermissions(value = {"warehouse-logistics:add"}, logical = Logical.OR)
    public CommonResult<Void> logisticsSave(@RequestBody @Validated WarehouseLogisticsSaveInput input) {
        warehouseLogisticsService.logisticsSave(input);
        return CommonResult.ok();
    }

    /**
     * 城配仓截单时间的取消
     */
    @PostMapping("/upsert/close-time-cancel")
    public CommonResult<Void> closeTimeCancel(@RequestParam Integer storeNo) {
        warehouseLogisticsService.closeTimeCancel(storeNo);
        return CommonResult.ok();
    }

    /**
     * 获取所有的城配仓
     */
    @PostMapping("/query/logistics-all")
    public CommonResult<List<WarehouseLogisticsAllVO>> queryLogisticsAll(Integer status) {
        return CommonResult.ok(warehouseLogisticsService.queryLogisticsAll(status).stream().map(WarehouseLogisticsCenterVOConverter::allDto2VoList).collect(Collectors.toList()));
    }

    /**
     * 查询城配仓履约类型
     */
    @PostMapping("/query/logistics-fulfillment-type")
    public CommonResult<List<LogisticsFulfillmentTypeVO>> queryLogisticsFulfillmentType() {
        WarehouseLogisticsCenterEnums.FulfillmentType[] values = WarehouseLogisticsCenterEnums.FulfillmentType.values();
        List<LogisticsFulfillmentTypeVO> fulfillmentTypes = new ArrayList<>();

        for (WarehouseLogisticsCenterEnums.FulfillmentType value : values) {
            LogisticsFulfillmentTypeVO fulfillmentType = new LogisticsFulfillmentTypeVO();
            fulfillmentType.setType(value.getValue());
            fulfillmentType.setTypeName(value.getContent());
            fulfillmentTypes.add(fulfillmentType);
        }
        return CommonResult.ok(fulfillmentTypes);
    }
}
